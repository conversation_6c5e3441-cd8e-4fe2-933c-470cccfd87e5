package com.dhcc.dsp.publish.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dhcc.avatar.common.MethodOpenApi;
import com.dhcc.avatar.util.CommonUtil;
import com.dhcc.avatar.util.sm.SM4Utils;
import com.dhcc.dsp.business.datacollection.model.DataCollection;
import com.dhcc.dsp.business.datacollection.service.DataCollectionService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.exception.BusinessException;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api(value = "数据集相关的服务", tags = "数据集相关的服务")
@RestController
@RequestMapping("${api.version}/publish/dataCollection")
public class PublishDataCollectionController {

    private static final Log log = LogFactory.get();

    private final DataCollectionService dataCollectionService;

    public PublishDataCollectionController(DataCollectionService dataCollectionService) {
        this.dataCollectionService = dataCollectionService;
    }


    @ApiOperation(value = "查询数据集明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "数据集名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataCondition", value = "查询条件，按照{'condition': '具体的查询条件'}传入", dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "page", value = "分页参数，当前页数", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "分页参数，每页条数", required = true, dataType = "Integer", paramType = "query")
    })
    @MethodOpenApi
    @PostMapping("/getEncryDataDetail")
    public String getEncryDataDetail(HttpServletRequest request, String name, boolean convertEnName, @Nullable @RequestBody String dataCondition, Integer page, Integer limit) {
        String token = BaseUtil.getTokenFromHeader(request);

        // 处理 condition 字段的 base64 解码（兼容编码和未编码两种情况）
        if (StrUtil.isNotBlank(dataCondition)) {
            try {
                JSONObject conditionObj = JSONUtil.parseObj(dataCondition);
                String condition = conditionObj.getStr("condition");

                // 如果 condition 字段存在且不为空，尝试进行 base64 解码
                if (StrUtil.isNotBlank(condition)) {
                    // 检测是否为 base64 编码，如果是则解码，否则直接使用原值
                    if (CommonUtil.isBase64(condition)) {
                        try {
                            String decodedCondition = CommonUtil.parameterDeCode(condition);
                            conditionObj.set("condition", decodedCondition);
                            dataCondition = JSONUtil.toJsonStr(conditionObj);
                            log.debug("检测到 base64 编码的查询条件，已自动解码");
                        } catch (Exception decodeException) {
                            log.warn("base64 解码失败，使用原始条件: {}", decodeException.getMessage());
                            // 解码失败时使用原始条件，不抛出错误
                        }
                    } else {
                        log.debug("检测到未编码的查询条件，直接使用");
                    }
                }
            } catch (Exception e) {
                log.error("处理查询条件时发生错误: {}", e.getMessage());
                throw new BusinessException("查询条件格式错误");
            }
        }
        Map<String, Object> rsMap = dataCollectionService.dataCollectionDetail(token, null, name, 0, convertEnName, dataCondition, page, limit);

        JsonResult ok = JsonResult.ok();
        ok.put("data", rsMap.get("data"))
        .put("total", rsMap.get("total"))
        .put("column", rsMap.get("column"))
        .put("columnTypes", rsMap.get("columnTypes"));
        String result;
        try{
            String key= SM4Utils.generateBase64StringKey();
            String encrypt = SM4Utils.encrypt(key,JSONUtil.toJsonStr(ok));
            result=key+encrypt;
        }catch (Exception e){
            throw new BusinessException(e.getMessage());
        }
        return result;
    }


    @ApiOperation(value = "查询数据集明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "数据集名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataCondition", value = "查询条件，按照{'condition': '具体的查询条件'}传入", dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "page", value = "分页参数，当前页数", required = true, dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "分页参数，每页条数", required = true, dataType = "Integer", paramType = "query")
    })
    @MethodOpenApi
    @PostMapping("/getDataDetail")
    public Map<String, Object> getDataDetail(HttpServletRequest request, String name, boolean convertEnName, @Nullable @RequestBody String dataCondition, Integer page, Integer limit) {
        String token = BaseUtil.getTokenFromHeader(request);

        // 处理 condition 字段的 base64 解码（兼容编码和未编码两种情况）
        if (StrUtil.isNotBlank(dataCondition)) {
            try {
                JSONObject conditionObj = JSONUtil.parseObj(dataCondition);
                String condition = conditionObj.getStr("condition");

                // 如果 condition 字段存在且不为空，尝试进行 base64 解码
                if (StrUtil.isNotBlank(condition)) {
                    // 检测是否为 base64 编码，如果是则解码，否则直接使用原值
                    if (CommonUtil.isBase64(condition)) {
                        try {
                            String decodedCondition = CommonUtil.parameterDeCode(condition);
                            conditionObj.set("condition", decodedCondition);
                            dataCondition = JSONUtil.toJsonStr(conditionObj);
                            log.debug("检测到 base64 编码的查询条件，已自动解码");
                        } catch (Exception decodeException) {
                            log.warn("base64 解码失败，使用原始条件: {}", decodeException.getMessage());
                            // 解码失败时使用原始条件，不抛出错误
                        }
                    } else {
                        log.debug("检测到未编码的查询条件，直接使用");
                    }
                }
            } catch (Exception e) {
                log.error("处理查询条件时发生错误: {}", e.getMessage());
                return JsonResult.error("查询条件格式错误");
            }
        }

        Map<String, Object> rsMap = dataCollectionService.dataCollectionDetail(token, null, name, 0, convertEnName, dataCondition, page, limit);
        return Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("data", rsMap.get("data"))).put("total", rsMap.get("total"))).put("column", rsMap.get("column")).put("columnTypes", rsMap.get("columnTypes"));
    }

    @ApiOperation(value = "查询数据集")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "数据集名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "describe", value = "数据集描述", required = false, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "people", value = "创建人", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始时间", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "数据集类型，多个用逗号分隔", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "groupId", value = "数据集分组", required = false, dataType = "String", paramType = "query")
    })
    @MethodOpenApi
    @GetMapping("/queryDataCollection")
    public Map<String, Object> queryDataCollection(String name, String describe, String people, String startDate, String endDate, String type, String groupId) {
        return JsonResult.ok().put("data", dataCollectionService.getCollection(name, describe, people, startDate, endDate, type, groupId, 0, null, null, null, null, null, null, null, null));
    }

    @ApiOperation(value = "查询数据集字段信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "数据集名称", required = true, dataType = "String", paramType = "query")
    })
    @MethodOpenApi
    @GetMapping("/getDataCollectionColumn")
    public Map<String, Object> getDataCollectionColumn(HttpServletRequest request, String name) {
        String token = BaseUtil.getTokenFromHeader(request);
        return JsonResult.ok().put("data", dataCollectionService.getCollectionColumn(token, name, null, null, true));
    }

    @ApiOperation(value = "更新数据集")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectionName", value = "数据集名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sync", value = "是否同步返回结果（明细数据集异步更新数据，如果需要同步返回，则更新完成以后返回操作成功，同步返回可能需要较长时间）", dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "modelName", value = "提交spark任务的模型名称", dataType = "String", paramType = "query"),
    })
    @MethodOpenApi
    @PostMapping("/updateDataCollection")
    public Map<String, Object> updateDataCollection(@NotBlank(message = "数据集名称不能为空") String collectionName, boolean sync, String modelName) {
        DataCollection dataCollection = dataCollectionService.getOne(new QueryWrapper<DataCollection>().eq("collection_name", collectionName).eq("collection_status", 0));
        if (dataCollection == null) {
            throw new BusinessException("数据集【{}】不存在", collectionName);
        }
        return JsonResult.ok().put("applicationId", dataCollectionService.updateDataCollection(dataCollection.getId(), sync, null, modelName, 0));
    }

    @ApiOperation(value = "查询数据集血缘")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectionName", value = "数据集名称", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "tableName", value = "表名称", required = false, dataType = "String", paramType = "query")
    })
    @MethodOpenApi
    @GetMapping("/getDataCollectionRelation")
    public Map<String, Object> getDataCollectionRelation(String collectionName, String tableName) {
        String collectionId = null;
        if (StrUtil.isNotBlank(collectionName)) {
            List<DataCollection> list = dataCollectionService.list(new QueryWrapper<DataCollection>().eq("collection_name", collectionName).eq("collection_status", 0));
            if (list.size() > 1) {
                throw new BusinessException("存在重名的数据集");
            }
            if (CollUtil.isEmpty(list)) {
                throw new BusinessException("未查询到对应的数据集");
            }
            collectionId = list.get(0).getId();
        } else if (StrUtil.isNotBlank(tableName)) {
            List<DataCollection> list = dataCollectionService.list(new QueryWrapper<DataCollection>().eq("collection_code", tableName.replace("data_collection_", "")).eq("collection_status", 0));
            if (list.size() > 1) {
                throw new BusinessException("存在code重复的数据集");
            }
            if (CollUtil.isEmpty(list)) {
                throw new BusinessException("未查询到对应的数据集");
            }
            collectionId = list.get(0).getId();
        }
        return JsonResult.ok().put("data", dataCollectionService.getRelationById(collectionId));
    }

    @GetMapping("/getDataCollectionByTableName")
    public Map<String, Object> getDataCollectionByTableName(String tableName) {
        List<DataCollection> list = dataCollectionService.list(new QueryWrapper<DataCollection>().select("id", "collection_name", "collection_simple_name", "collection_desc", "collection_code", "collection_status", "detail_name", "create_user", "create_time", "collection_en_name", "collection_logic_type").eq("collection_code", tableName.replace("data_collection_", "")).eq("collection_status", 0));
        if (list.size() > 1) {
            throw new BusinessException("存在code重复的数据集");
        }
        if (CollUtil.isEmpty(list)) {
            throw new BusinessException("未查询到对应的数据集");
        }
        return JsonResult.ok().put("data", list.get(0));
    }

    @ApiOperation(value = "新增或修改数据集的数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectionName", value = "数据集名称", required = true, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "dataRowList", value = "新增或修改的数据列表，示例[{\"主键字段\":\"修改值\",\"映射字段\":\"修改值\"},{\"主键字段\":\"修改值\",\"映射字段\":\"修改值\"}]", dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "full", value = "是否全量更新，空或者0表示否（增量更新），1表示全量，2表示只更新部分字段值 ", dataType = "String", paramType = "body"),
    })
    @MethodOpenApi
    @PostMapping("/insertOrUpdateDatas")
    public Map<String, Object> insertOrUpdateDatas(@RequestBody String param) {
        if (StrUtil.isBlank(param)) {
            param = "{}";
        }
        dataCollectionService.updateCollectionDatas(param);
        return JsonResult.ok();
    }

    @ApiOperation(value = "删除数据集的数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectionName", value = "数据集名称", required = true, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "dataRowList", value = "新增或修改的数据列表，示例[{\"主键字段\":\"修改值\",\"映射字段\":\"修改值\"},{\"主键字段\":\"修改值\",\"映射字段\":\"修改值\"}]", dataType = "String", paramType = "body"),
    })
    @MethodOpenApi
    @PostMapping("/deleteDatas")
    public Map<String, Object> deleteDatas(@RequestBody JSONObject param) {
        dataCollectionService.insertOrUpdateDatas(param, true, param.getBool("clearAllData", false));
        return JsonResult.ok();
    }

    @ApiOperation(value = "根据SQL条件删除数据集的数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectionName", value = "数据集名称", required = true, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "condition", value = "where后面带的条件", required = true, dataType = "String", paramType = "body"),
    })
    @MethodOpenApi
    @PostMapping("/deleteDatasWithCondition")
    public Map<String, Object> deleteDataWithCondition(@RequestBody JSONObject param) {
        dataCollectionService.deleteDatasWithCondition(param);
        return JsonResult.ok();
    }

    @ApiOperation(value = "修改数据集的数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectionName", value = "数据集名称", required = true, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "dataRowList", value = "修改的数据列表，示例[{\"主键字段\":\"修改值\",\"映射字段\":\"修改值\"},{\"主键字段\":\"修改值\",\"映射字段\":\"修改值\"}]", dataType = "String", paramType = "body"),
    })
    @MethodOpenApi
    @PostMapping("updateDatas")
    public Map<String, Object> updateDatas(@RequestBody JSONObject param) {
        dataCollectionService.updateDatas(param);
        return JsonResult.ok();
    }

    @ApiOperation(value = "获取数据集的数据源和sql")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "collectionName", value = "数据集名称", required = true, dataType = "String", paramType = "query"),
    })
    @MethodOpenApi
    @GetMapping("/getResourceSql")
    public Map<String, Object> getResourceSql(String collectionName) {
        return JsonResult.ok().put("data", dataCollectionService.getResourceSql(collectionName));
    }

    @PostMapping("/getColumnData")
    public Map<String, Object> getColumnData(@RequestBody JSONObject param) {
        return JsonResult.ok().put("data", dataCollectionService.getColumnData(param));
    }

    @PostMapping("/getAllCollectionName")
    public Map<String, Object> getAllCollectionName() {
        return JsonResult.ok().put("data", dataCollectionService.getAllCollectionName(0));
    }

    @PostMapping("/getCitedCollection")
    public Map<String, Object> getCitedCollection(String collectionId) {
        return JsonResult.ok().put("data", dataCollectionService.getCitedCollection(collectionId));
    }
}
