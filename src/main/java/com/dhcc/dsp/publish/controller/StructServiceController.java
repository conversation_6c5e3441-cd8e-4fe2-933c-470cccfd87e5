package com.dhcc.dsp.publish.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.common.MethodOpenApi;
import com.dhcc.avatar.util.CommonUtil;
import com.dhcc.avatar.util.OperationUtil;
import com.dhcc.avatar.util.UUIDUtil;
import com.dhcc.avatar.util.sm.SM4Utils;
import com.dhcc.dsp.business.service.OperationService;
import com.dhcc.dsp.business.service.StructDataService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.exception.BusinessException;
import com.dhcc.dsp.common.utils.BaseUtil;
import com.dhcc.dsp.system.service.SysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

@Api(value = "结构化视图相关的服务", tags = "结构化视图相关的服务")
@RestController
@RequestMapping("${api.version}/publish/structView")
public class StructServiceController {

    private static final Log log = LogFactory.get();

    private final StructDataService structDataService;
    private final SysConfigService sysConfigService;
    private final OperationService operationService;

    public StructServiceController(StructDataService structDataService, SysConfigService sysConfigService, OperationService operationService) {
        this.structDataService = structDataService;
        this.sysConfigService = sysConfigService;
        this.operationService = operationService;
    }

    @ApiOperation(value = "获取结构化视图中的具体数据对象数据（此接口可能会出现某些特殊字符问题，建议使用post接口）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSetName", value = "结构化数据视图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetSimpleName", value = "数据视图简称，视图名称为空的情况下，简称和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetCode", value = "数据视图编码，视图名称为空的情况下，编码和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetStatus", value = "数据视图状态，视图名称为空的情况下，简称和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataObjName", value = "结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "replaceHolder", value = "数据对象占位符对应值，多个占位符值用英文逗号隔开，注意占位符值的顺序要和数据对象定义的占位符顺序一致", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "forceFetch", value = "读取缓存参数，参数为0或1，可以为空，默认为1，如果参数为空或者为1，数据对象的forceFetch都为1，否则数据对象的forceFetch为0", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "returnColumnList", value = "返回的字段列表，多个字段用逗号隔开，注意字段必须在结构化视图的attrInfoString字段中", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "extraDataCondition", value = "额外的搜索条件，格式为 字段= '筛选值' and 字段2 != '筛选值'，注意这里的字段必须在结构化视图的attrInfoString字段中，但不在结构化视图的forbidAbbrList字段中 ", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderByExpression", value = "排序表达式，示例  字段 desc/asc，注意字段必须在结构化视图的attrInfoString字段中", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "结果分页参数，当前页数", required = true, dataType = "Int", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "limit", value = "结果分页参数，每页数据条数", required = true, dataType = "Int", paramType = "query", example = "10")
    })
    @MethodOpenApi
    @GetMapping("/queryStructDataDetail")
    public Map<String, Object> queryStructDataDetail(HttpServletRequest request, String dataSetName, String dataSetSimpleName, String dataSetCode, String dataSetStatus, String dataObjName, String replaceHolder, String placeholderSeparator, String forceFetch, String returnColumnList, String extraDataCondition, String orderByExpression, boolean convertEnName, Integer page, Integer limit) {
        log.info("开始获取结构化视图中的具体数据对象数据");
        String token = BaseUtil.getTokenFromHeader(request);
        dataSetName = URLUtil.decode(dataSetName);
        dataObjName = URLUtil.decode(dataObjName);
        returnColumnList = URLUtil.decode(returnColumnList);
        extraDataCondition = URLUtil.decode(extraDataCondition);
        orderByExpression = URLUtil.decode(orderByExpression);
        Map<String, Object> rsMap = structDataService.getStructDataDetail(BaseUtil.getIpAdrress(request), token, dataSetName, dataSetSimpleName, dataSetCode, dataSetStatus, dataObjName, "", replaceHolder, placeholderSeparator, forceFetch, returnColumnList, extraDataCondition, orderByExpression, false, convertEnName, true, null, page, limit);
        log.info("结构化视图中的具体数据对象数据获取完毕");
        JsonResult ok = JsonResult.ok();
        ok.put("total", rsMap.get("total"));
        ok.put("metaAttrTypes", rsMap.get("metaAttrTypes"));
        ok.put("metaAttrNames", rsMap.get("metaAttrNames"));
        ok.put("data", rsMap.get("data"));
        ok.put("placeholderDetail", rsMap.get("placeholderDetail"));
        return ok;
    }

    @ApiOperation(value = "获取结构化视图中的具体数据对象数据（国能投使用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSetName", value = "结构化数据视图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataObjName", value = "结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "enDataObjName", value = "结构化数据对象英文名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "withType", value = "是否按照类型返回数据，0表示全部以字符串类型返回", dataType = "Int", paramType = "query", defaultValue = "1"),
            @ApiImplicitParam(name = "replaceHolder", value = "数据对象占位符对应值，多个占位符值用英文逗号隔开，注意占位符值的顺序要和数据对象定义的占位符顺序一致", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "forceFetch", value = "读取缓存参数，参数为0或1，可以为空，默认为1，如果参数为空或者为1，数据对象的forceFetch都为1，否则数据对象的forceFetch为0", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "returnColumnList", value = "返回的字段列表，多个字段用逗号隔开，注意字段必须在结构化视图的attrInfoString字段中", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "extraDataCondition", value = "额外的搜索条件，格式为 字段= '筛选值' and 字段2 != '筛选值'，注意这里的字段必须在结构化视图的attrInfoString字段中，但不在结构化视图的forbidAbbrList字段中 ", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderByExpression", value = "排序表达式，示例  字段 desc/asc，注意字段必须在结构化视图的attrInfoString字段中", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "convertEnName", value = "是否以英文字段返回，1返回英文字段，0返回中文字段，默认0", dataType = "Int", paramType = "query", defaultValue = "0"),
            @ApiImplicitParam(name = "maxWait", value = "延长系统等待时间，默认3600s", dataType = "Int", paramType = "query", defaultValue = "3600"),
            @ApiImplicitParam(name = "page", value = "结果分页参数，当前页数", dataType = "Int", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "limit", value = "结果分页参数，每页数据条数", dataType = "Int", paramType = "query", example = "10")
    })
    @MethodOpenApi
    @GetMapping("/queryStructDataDetailByGNT")
    public Map<String, Object> queryStructDataDetailByGNT(HttpServletRequest request, String dataSetName, String dataObjName, String enDataObjName, String replaceHolder, String placeholderSeparator, String forceFetch, String returnColumnList, String extraDataCondition, String orderByExpression, Integer convertEnName, Integer withType, Integer maxWait, Integer page, Integer limit) {
        String token = BaseUtil.getTokenFromHeader(request);
        String ipAdrress = BaseUtil.getIpAdrress(request);
        dataSetName = URLUtil.decode(dataSetName);
        dataObjName = URLUtil.decode(dataObjName);
        returnColumnList = URLUtil.decode(returnColumnList);
        extraDataCondition = URLUtil.decode(extraDataCondition);
        orderByExpression = URLUtil.decode(orderByExpression);
        boolean isConvertEnName = convertEnName != null && convertEnName == 1;
        boolean isWithType = withType == null || withType != 0;
        Map<String, Object> rsMap = structDataService.getStructDataDetail(ipAdrress, token, dataSetName, "", "", "", dataObjName, enDataObjName, replaceHolder, placeholderSeparator, forceFetch, returnColumnList, extraDataCondition, orderByExpression, true, isConvertEnName, isWithType, maxWait, page, limit);
        JsonResult ok = JsonResult.ok();
        ok.put("total", rsMap.get("total"));
        ok.put("metaAttrTypes", rsMap.get("metaAttrTypes"));
        ok.put("metaAttrNames", rsMap.get("metaAttrNames"));
        ok.put("data", rsMap.get("data"));
        ok.put("placeholderDetail", rsMap.get("placeholderDetail"));
        ok.put("pkMetaAttrNames", rsMap.get("pkMetaAttrNames"));
        return ok;
    }

    @ApiOperation(value = "获取结构化视图中的具体数据对象数据（国能投使用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "condition", value = "结构化视图的查询条件，以json的格式传入，json格式为：{\n" +
                    "    \"dataSetName\": \"结构化数据视图名称\",\n" +
                    "    \"dataObjName\": \"结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）\",\n" +
                    "    \"enDataObjName\": \"结构化数据对象英文名称\",\n" +
                    "    \"withType\": \"是否按照类型返回数据，0表示全部以字符串类型返回\",\n" +
                    "    \"replaceHolder\": \"数据对象占位符对应值，多个占位符值用英文逗号隔开，注意占位符值的顺序要和数据对象定义的占位符顺序一致\",\n" +
                    "    \"forceFetch\": \"读取缓存参数，参数为0或1，可以为空，默认为1，如果参数为空或者为1，数据对象的forceFetch都为1，否则数据对象的forceFetch为0\",\n" +
                    "    \"returnColumnList\": \"返回的字段列表，多个字段用逗号隔开，注意字段必须在结构化视图的attrInfoString字段中\",\n" +
                    "    \"extraDataCondition\": \"额外的搜索条件，格式为 字段= '筛选值' and 字段2 != '筛选值'，注意这里的字段必须在结构化视图的attrInfoString字段中，但不在结构化视图的forbidAbbrList字段中\",\n" +
                    "    \"orderByExpression\": \"排序表达式，示例  字段 desc/asc，注意字段必须在结构化视图的attrInfoString字段中\",\n" +
                    "    \"convertEnName\": \"是否以英文字段返回，1返回英文字段，0返回中文字段，默认0\",\n" +
                    "    \"maxWait\": \"延长系统等待时间，默认3600s\",\n" +
                    "    \"page\": \"结果分页参数，当前页数\",\n" +
                    "    \"limit\": \"结果分页参数，每页数据条数\",\n" +
                    "}", required = true, dataType = "string", paramType = "body")
    })
    @MethodOpenApi
    @PostMapping("/queryStructDataDetailByGNT")
    public Map<String, Object> queryStructDataDetailByGNT(HttpServletRequest request, @NotBlank(message = "条件不允许为空") @RequestBody String condition) {
        String token = BaseUtil.getTokenFromHeader(request);
        String ipAdrress = BaseUtil.getIpAdrress(request);
        JSONObject jsonObject = JSONUtil.parseObj(condition);
        String dataSetName = StrUtil.trimToEmpty(jsonObject.getStr("dataSetName"));
        if (StrUtil.isBlank(dataSetName)) {
            return JsonResult.error("视图名称不能为空");
        }
        String enDataObjName = StrUtil.trimToEmpty(jsonObject.getStr("enDataObjName"));
        String dataObjName = StrUtil.trimToEmpty(jsonObject.getStr("dataObjName"));
        String replaceHolder = StrUtil.trimToEmpty(jsonObject.getStr("replaceHolder"));
        String placeholderSeparator = jsonObject.getStr("placeholderSeparator");
        String forceFetch = StrUtil.trimToEmpty(jsonObject.getStr("forceFetch"));
        String returnColumnList = StrUtil.trimToEmpty(jsonObject.getStr("returnColumnList"));
        String extraDataCondition = StrUtil.trimToEmpty(jsonObject.getStr("extraDataCondition"));
        String orderByExpression = StrUtil.trimToEmpty(jsonObject.getStr("orderByExpression"));
        Integer convertEnName = jsonObject.getInt("convertEnName", 0);
        boolean isConvertEnName = convertEnName != null && convertEnName == 1;
        Integer withType = jsonObject.getInt("withType", null);
        boolean isWithType = withType == null || withType != 0;
        Integer maxWait = jsonObject.getInt("maxWait", null);
        Integer page = jsonObject.getInt("page");
        Integer limit = jsonObject.getInt("limit");
        Map<String, Object> rsMap = structDataService.getStructDataDetail(ipAdrress, token, dataSetName, "", "", "", dataObjName, enDataObjName, replaceHolder, placeholderSeparator, forceFetch, returnColumnList, extraDataCondition, orderByExpression, true, isConvertEnName, isWithType, maxWait, page, limit);
        JsonResult ok = JsonResult.ok();
        ok.put("total", rsMap.get("total"));
        ok.put("metaAttrTypes", rsMap.get("metaAttrTypes"));
        ok.put("metaAttrNames", rsMap.get("metaAttrNames"));
        ok.put("data", rsMap.get("data"));
        ok.put("placeholderDetail", rsMap.get("placeholderDetail"));
        ok.put("pkMetaAttrNames", rsMap.get("pkMetaAttrNames"));
        return ok;
    }

    @ApiOperation(value = "获取结构化视图中的具体数据对象数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "condition", value = "结构化视图的查询条件，以json的格式传入，json格式为：{\n" +
                    "    \"dataSetName\": \"结构化数据视图名称\",\n" +
                    "    \"dataSetSimpleName\": \"结构化数据视图简称，视图名称为空的情况下，简称和状态必须输入\",\n" +
                    "    \"dataSetCode\": \"结构化数据视图编码，视图名称为空的情况下，编码和状态必须输入\",\n" +
                    "    \"dataSetStatus\": \"结构化数据视图状态，视图名称为空的情况下，简称和状态必须输入\",\n" +
                    "    \"dataObjName\": \"结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）\",\n" +
                    "    \"replaceHolder\": \"数据对象占位符对应值，多个占位符值用英文逗号隔开，注意占位符值的顺序要和数据对象定义的占位符顺序一致\",\n" +
                    "    \"forceFetch\": \"读取缓存参数，参数为0或1，可以为空，默认为1，如果参数为空或者为1，数据对象的forceFetch都为1，否则数据对象的forceFetch为0\",\n" +
                    "    \"returnColumnList\": \"返回的字段列表，多个字段用逗号隔开，注意字段必须在结构化视图的attrInfoString字段中\",\n" +
                    "    \"extraDataCondition\": \"额外的搜索条件，格式为 字段= '筛选值' and 字段2 != '筛选值'，注意这里的字段必须在结构化视图的attrInfoString字段中，但不在结构化视图的forbidAbbrList字段中\",\n" +
                    "    \"orderByExpression\": \"排序表达式，示例  字段 desc/asc，注意字段必须在结构化视图的attrInfoString字段中\",\n" +
                    "    \"convertEnName\": \"结构化数据视图是以英文字段返回，true返回英文字段，false返回中文字段，默认false\",\n" +
                    "    \"page\": \"结果分页参数，当前页数\",\n" +
                    "    \"limit\": \"结果分页参数，每页数据条数\",\n" +
                    "}", required = true, dataType = "string", paramType = "body")
    })
    @MethodOpenApi
    @PostMapping("/queryStructDataDetail")
    public Map<String, Object> queryStructDataDetail(HttpServletRequest request, @NotBlank(message = "条件不允许为空") @RequestBody String condition) {
        String token = BaseUtil.getTokenFromHeader(request);
        JSONObject jsonObject = JSONUtil.parseObj(condition);
        String dataSetName = StrUtil.trimToEmpty(jsonObject.getStr("dataSetName"));
        String dataSetSimpleName = StrUtil.trimToEmpty(jsonObject.getStr("dataSetSimpleName"));
        String dataSetStatus = StrUtil.trimToEmpty(jsonObject.getStr("dataSetStatus"));
        String dataSetCode = StrUtil.trimToEmpty(jsonObject.getStr("dataSetCode"));
        if (StrUtil.isAllBlank(dataSetName, dataSetSimpleName, dataSetCode) || StrUtil.isAllBlank(dataSetName, dataSetStatus)) {
            return JsonResult.error("视图名称为空时，视图简称和视图状态必须输入");
        }
        String dataObjName = StrUtil.trimToEmpty(jsonObject.getStr("dataObjName"));
        if (StrUtil.isBlank(dataObjName)) {
            return JsonResult.error("视图中的对象名称为空");
        }
        String replaceHolder = StrUtil.trimToEmpty(jsonObject.getStr("replaceHolder"));
        String placeholderSeparator = jsonObject.getStr("placeholderSeparator");
        String forceFetch = StrUtil.trimToEmpty(jsonObject.getStr("forceFetch"));
        String returnColumnList = StrUtil.trimToEmpty(jsonObject.getStr("returnColumnList"));
        String extraDataCondition = StrUtil.trimToEmpty(jsonObject.getStr("extraDataCondition"));
        // 长江电力需判断额外搜索条件是否经过编码
        if (sysConfigService.getEncodeSwitch()) {
            if (StrUtil.isNotBlank(extraDataCondition) && !CommonUtil.isBase64(extraDataCondition)) {
                return JsonResult.error("额外搜索条件未编码或编码不正确");
            }
            if (StrUtil.isNotBlank(replaceHolder) && !CommonUtil.isBase64(replaceHolder)) {
                return JsonResult.error("占位符信息未编码或编码不正确");
            }
        }
        extraDataCondition = CommonUtil.parameterDeCode(extraDataCondition);
        replaceHolder = CommonUtil.parameterDeCode(replaceHolder);
        log.info("extraDataCondition: {}", extraDataCondition);
        String orderByExpression = StrUtil.trimToEmpty(jsonObject.getStr("orderByExpression"));
        boolean convertEnName = jsonObject.getBool("convertEnName", false);
        Integer page = jsonObject.getInt("page");
        Integer limit = jsonObject.getInt("limit");
        Map<String, Object> rsMap = structDataService.getStructDataDetail(BaseUtil.getIpAdrress(request), token, dataSetName, dataSetSimpleName, dataSetCode, dataSetStatus, dataObjName, "", replaceHolder, placeholderSeparator, forceFetch, returnColumnList, extraDataCondition, orderByExpression, false, convertEnName, true, null, page, limit);
        JsonResult ok = JsonResult.ok();
        ok.put("total", rsMap.get("total"));
        ok.put("metaAttrTypes", rsMap.get("metaAttrTypes"));
        ok.put("metaAttrNames", rsMap.get("metaAttrNames"));
        ok.put("data", rsMap.get("data"));
        ok.put("placeholderDetail", rsMap.get("placeholderDetail"));
        return ok;
    }


    @ApiOperation(value = "获取结构化视图中的具体数据对象数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "condition", value = "结构化视图的查询条件，以json的格式传入，json格式为：{\n" +
                    "    \"dataSetName\": \"结构化数据视图名称\",\n" +
                    "    \"dataSetSimpleName\": \"结构化数据视图简称，视图名称为空的情况下，简称和状态必须输入\",\n" +
                    "    \"dataSetCode\": \"结构化数据视图编码，视图名称为空的情况下，编码和状态必须输入\",\n" +
                    "    \"dataSetStatus\": \"结构化数据视图状态，视图名称为空的情况下，简称和状态必须输入\",\n" +
                    "    \"dataObjName\": \"结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）\",\n" +
                    "    \"replaceHolder\": \"数据对象占位符对应值，多个占位符值用英文逗号隔开，注意占位符值的顺序要和数据对象定义的占位符顺序一致\",\n" +
                    "    \"forceFetch\": \"读取缓存参数，参数为0或1，可以为空，默认为1，如果参数为空或者为1，数据对象的forceFetch都为1，否则数据对象的forceFetch为0\",\n" +
                    "    \"returnColumnList\": \"返回的字段列表，多个字段用逗号隔开，注意字段必须在结构化视图的attrInfoString字段中\",\n" +
                    "    \"extraDataCondition\": \"额外的搜索条件，格式为 字段= '筛选值' and 字段2 != '筛选值'，注意这里的字段必须在结构化视图的attrInfoString字段中，但不在结构化视图的forbidAbbrList字段中\",\n" +
                    "    \"orderByExpression\": \"排序表达式，示例  字段 desc/asc，注意字段必须在结构化视图的attrInfoString字段中\",\n" +
                    "    \"convertEnName\": \"结构化数据视图是以英文字段返回，true返回英文字段，false返回中文字段，默认false\",\n" +
                    "    \"page\": \"结果分页参数，当前页数\",\n" +
                    "    \"limit\": \"结果分页参数，每页数据条数\",\n" +
                    "}", required = true, dataType = "string", paramType = "body")
    })
    @MethodOpenApi
    @PostMapping("/queryEncryStructDataDetail")
    public String queryEncryStructDataDetail(HttpServletRequest request, @NotBlank(message = "条件不允许为空") @RequestBody String condition) {
        String token = BaseUtil.getTokenFromHeader(request);
        JSONObject jsonObject = JSONUtil.parseObj(condition);
        String dataSetName = StrUtil.trimToEmpty(jsonObject.getStr("dataSetName"));
        String dataSetSimpleName = StrUtil.trimToEmpty(jsonObject.getStr("dataSetSimpleName"));
        String dataSetStatus = StrUtil.trimToEmpty(jsonObject.getStr("dataSetStatus"));
        String dataSetCode = StrUtil.trimToEmpty(jsonObject.getStr("dataSetCode"));
        if (StrUtil.isAllBlank(dataSetName, dataSetSimpleName, dataSetCode) || StrUtil.isAllBlank(dataSetName, dataSetStatus)) {
            throw new BusinessException("视图名称为空时，视图简称和视图状态必须输入");
        }
        String dataObjName = StrUtil.trimToEmpty(jsonObject.getStr("dataObjName"));
        if (StrUtil.isBlank(dataObjName)) {
            throw new BusinessException( "视图中的对象名称为空");
        }

        String replaceHolder = StrUtil.trimToEmpty(jsonObject.getStr("replaceHolder"));
        String placeholderSeparator = jsonObject.getStr("placeholderSeparator");
        String forceFetch = StrUtil.trimToEmpty(jsonObject.getStr("forceFetch"));
        String returnColumnList = StrUtil.trimToEmpty(jsonObject.getStr("returnColumnList"));
        String extraDataCondition = StrUtil.trimToEmpty(jsonObject.getStr("extraDataCondition"));
        // 长江电力需判断额外搜索条件是否经过编码
        if (sysConfigService.getEncodeSwitch()) {
            if (StrUtil.isNotBlank(extraDataCondition) && !CommonUtil.isBase64(extraDataCondition)) {
                throw new BusinessException( "额外搜索条件未编码或编码不正确");
            }
            if (StrUtil.isNotBlank(replaceHolder) && !CommonUtil.isBase64(replaceHolder)) {
                throw new BusinessException( "占位符信息未编码或编码不正确");
            }
        }
        extraDataCondition = CommonUtil.parameterDeCode(extraDataCondition);
        replaceHolder = CommonUtil.parameterDeCode(replaceHolder);
        log.info("extraDataCondition: {}", extraDataCondition);
        String orderByExpression = StrUtil.trimToEmpty(jsonObject.getStr("orderByExpression"));
        boolean convertEnName = jsonObject.getBool("convertEnName", false);
        Integer page = jsonObject.getInt("page");
        Integer limit = jsonObject.getInt("limit");
        Map<String, Object> rsMap = structDataService.getStructDataDetail(BaseUtil.getIpAdrress(request), token, dataSetName, dataSetSimpleName, dataSetCode, dataSetStatus, dataObjName, "", replaceHolder, placeholderSeparator, forceFetch, returnColumnList, extraDataCondition, orderByExpression, false, convertEnName, true, null, page, limit);
        JsonResult ok = JsonResult.ok();
        ok.put("total", rsMap.get("total"));
        ok.put("metaAttrTypes", rsMap.get("metaAttrTypes"));
        ok.put("metaAttrNames", rsMap.get("metaAttrNames"));
        ok.put("data", rsMap.get("data"));
        ok.put("placeholderDetail", rsMap.get("placeholderDetail"));
        String result;
        try{
            String key=SM4Utils.generateBase64StringKey();
            String encrypt = SM4Utils.encrypt(key,JSONUtil.toJsonStr(ok));
            result=key+encrypt;
        }catch (Exception e){
            throw new BusinessException(e.getMessage());
        }
        return result;
    }

    @ApiOperation(value = "下载结构化视图中的具体数据对象数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSetName", value = "结构化数据视图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataObjName", value = "结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "replaceHolder", value = "数据对象占位符对应值，多个占位符值用英文逗号隔开，注意占位符值的顺序要和数据对象定义的占位符顺序一致", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "returnColumnList", value = "返回的字段列表，多个字段用逗号隔开，注意字段必须在结构化视图的attrInfoString字段中", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "extraDataCondition", value = "额外的搜索条件，格式为 字段= '筛选值' and 字段2 != '筛选值'，注意这里的字段必须在结构化视图的attrInfoString字段中，但不在结构化视图的forbidAbbrList字段中 ", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderByExpression", value = "排序表达式，示例  字段 desc/asc，注意字段必须在结构化视图的attrInfoString字段中", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "结果分页参数，当前页数", required = true, dataType = "Int", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "limit", value = "结果分页参数，每页数据条数", required = true, dataType = "Int", paramType = "query", example = "10")
    })
    @MethodOpenApi
    @GetMapping("/downloadStructDataDetail")
    public void downloadStructDataDetail(HttpServletRequest request, HttpServletResponse response, String dataSetName, String dataObjName, String replaceHolder, String placeholderSeparator, String returnColumnList, String extraDataCondition, String orderByExpression, boolean convertEnName, Integer page, Integer limit) {
        String token = BaseUtil.getTokenFromHeader(request);
        String ipAdrress = BaseUtil.getIpAdrress(request);
        dataSetName = URLUtil.decode(dataSetName);
        dataObjName = URLUtil.decode(dataObjName);
        returnColumnList = URLUtil.decode(returnColumnList);
        extraDataCondition = URLUtil.decode(extraDataCondition);
        orderByExpression = URLUtil.decode(orderByExpression);
        structDataService.downloadStructDataDetail(ipAdrress, response, token, dataSetName, dataObjName, replaceHolder, placeholderSeparator, returnColumnList, extraDataCondition, orderByExpression, convertEnName, page, limit);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "更新结构化视图数据具体数据，只支持数据对象、自定义数据集、数据对象数据集创建的结构化视图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSetName", value = "结构化数据视图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataObjName", value = "结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "full", value = "是否全量更新，空或者0表示否（增量更新），1表示全量，2表示只更新部分字段值", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "updateExpression", value = "更新的字段JSON，注意，1.更新的字段值数据中必须包含主键和值；" +
                    "2.增量更新时，如果存在对应主键数据，会根据主键值更新其他字段值，否则新增数据；对于全量更新，先删除数据，再进行新增；" +
                    "3.增量更新和全量更新时，必须包含结构化视图的attrInfoString所有字段；只更新部分字段值时，包含主键及要更新的字段值；4.示例[{\"主键\":\"值\",\"字段名\":\"值\"},{\"主键\":\"值\",\"字段\":\"值\"}] ", required = true, dataType = "String", paramType = "body")
    })
    @MethodOpenApi
    @PostMapping("/updateStructDataDetail")
    public Map<String, Object> updateStructDataDetail(HttpServletRequest request, String dataSetName, String dataObjName, String full, @RequestBody String updateExpression) {
        log.info("开始更新结构化视图数据具体数据");
        String token = BaseUtil.getTokenFromHeader(request);
        structDataService.updateStructDataDetail(BaseUtil.getIpAdrress(request), token, dataSetName, dataObjName, full, updateExpression);
        log.info("结构化视图数据具体数据更新完毕");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.UPDATE, StrUtil.format("{} : {}", dataSetName, updateExpression), "结构化视图具体数据更新");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "删除结构化视图数据具体数据，只支持数据对象、自定义数据集、数据对象数据集创建的结构化视图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSetName", value = "结构化数据视图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataObjName", value = "结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "0表示按照主键删除，1表示按照表达式删除，2表示全表删除", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "updateExpression", value = "如果type是0, 示例[{\"主键\":\"值\",\"字段名\":\"值\"},{\"主键\":\"值\",\"字段\":\"值\"}], 如果type是1，传入where条件，示例{\"updateExpression\": \"id = '11111111111'\"}, 如果type是2可以不传数据", dataType = "String", paramType = "body")
    })
    @MethodOpenApi
    @PostMapping("/deleteStructDataDetail")
    public Map<String, Object> deleteStructDataDetail(HttpServletRequest request, String dataSetName, String dataObjName, String type, @RequestBody String updateExpression) {
        log.info("开始更新结构化视图数据具体数据");
        String token = BaseUtil.getTokenFromHeader(request);
        structDataService.deleteStructDataDetail(token, dataSetName, dataObjName, type, updateExpression);
        log.info("结构化视图数据具体数据更新完毕");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.DELETE, StrUtil.format("{} : {}", dataSetName, updateExpression), "结构化视图具体数据删除");
        return JsonResult.ok();
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "结构化视图具体数据导图，只支持自定义数据集")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSetName", value = "结构化数据视图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataObjName", value = "结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "full", value = "是否全量导入，true表示全量，false表示增量", dataType = "boolean", paramType = "query"),
            @ApiImplicitParam(name = "file", value = "具体数据excel文件", dataType = "file", paramType = "body")
    })
    @MethodOpenApi
    @PostMapping("/uploadStructDataDetail")
    public Map<String, Object> uploadStructDataDetail(HttpServletRequest request, String dataSetName, String dataObjName, boolean full, MultipartFile file) {
        log.info("开始导入结构化视图数据具体数据");
        String token = BaseUtil.getTokenFromHeader(request);
        structDataService.uploadStructDataDetail(token, dataSetName, dataObjName, full, file);
        log.info("结构化视图数据具体数据导入完毕");
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.UPDATE, StrUtil.format("{} : {}", dataSetName, file.getName()), "结构化视图具体数据导入");
        return JsonResult.ok();
    }

    @ApiOperation(value = "结构化视图具体数据导图，只支持自定义数据集")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSetName", value = "结构化数据视图名称", required = true, dataType = "String", paramType = "query"),
    })
    @MethodOpenApi
    @PostMapping("/getStructSetObjectNameList")
    public Map<String, Object> getStructSetObjectNameList(String dataSetName) {
        log.info("开始导入结构化视图数据具体数据");
        List<String> objectNameList = structDataService.getStructSetObjectNameList(dataSetName);
        log.info("结构化视图数据具体数据导入完毕");
        return JsonResult.ok().put("data", objectNameList);
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "通过数据视图名称同步对应数据集详情数据, 只支持由数据集创建的结构化视图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSetName", value = "结构化视图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sync", value = "是否同步返回结果（明细数据集异步更新数据，如果需要同步返回，则更新完成以后返回操作成功，同步返回可能需要较长时间）", dataType = "boolean", paramType = "query")
    })
    @MethodOpenApi
    @PostMapping("/updateDataCollection")
    public Map<String, Object> updateDataCollection(@NotBlank(message = "结构化视图名称不能为空") String dataSetName, boolean sync) {
        structDataService.updateDataCollection(dataSetName, sync);
        operationService.saveOperation(UUIDUtil.generateUUID(), OperationUtil.operationType.UPDATE, dataSetName, "同步视图对应的数据集具体数据");
        return JsonResult.ok();
    }

    @ApiOperation(value = "获取结构化视图中对象数据的列信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSetName", value = "结构化数据视图名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetSimpleName", value = "数据视图简称，视图名称为空的情况下，简称和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetCode", value = "数据视图编码，视图名称为空的情况下，编码和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetStatus", value = "数据视图状态，视图名称为空的情况下，简称和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataObjName", value = "结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）", required = true, dataType = "String", paramType = "query"),
    })
    @MethodOpenApi
    @GetMapping("/queryStructDataColumn")
    public Map<String, Object> queryStructDataColumn(HttpServletRequest request, String dataSetName, String dataSetSimpleName, String dataSetCode, String dataSetStatus, String dataObjName) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataSetName = URLUtil.decode(dataSetName);
        dataObjName = URLUtil.decode(dataObjName);
        List<Map<String, Object>> structDataColumn = structDataService.getStructDataColumn(token, dataSetName, dataSetSimpleName, dataSetCode, dataSetStatus, dataObjName);
        return JsonResult.ok().put("data", structDataColumn);
    }

    @ApiOperation(value = "获取数据对象总数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataObjName", value = "结构化数据对象名称（对应数据视图查询中返回的dataObjName，或者使用对应数据视图查询中返回的path字段以点号分隔的最后一部分）", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "enDataObjName", value = "结构化数据对象英文名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "flag", value = "是否查询国能投数据对象(国能投数据对象会在英文名称前加上前缀\"lake_\")", required = true, defaultValue = "false", dataType = "boolean", paramType = "query"),
    })
    @MethodOpenApi
    @GetMapping("/getMetaObjDataCount")
    public Map<String, Object> getMetaObjDataCount(HttpServletRequest request, String dataObjName, String enDataObjName, Boolean flag) {
        String token = BaseUtil.getTokenFromHeader(request);
        dataObjName = URLUtil.decode(dataObjName);
        Integer total = structDataService.getMetaObjDataCount(token, dataObjName, enDataObjName, flag);
        return JsonResult.ok().put("data", total);
    }
}
