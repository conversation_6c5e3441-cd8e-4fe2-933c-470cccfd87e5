package com.dhcc.dsp.publish.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.common.MethodOpenApi;
import com.dhcc.avatar.domain.AvatarThreadContext;
import com.dhcc.avatar.util.sm.SM4Utils;
import com.dhcc.dsp.business.service.DataSetService;
import com.dhcc.dsp.business.service.StationService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.exception.BusinessException;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api(value = "时序数据相关的服务", tags = "时序数据相关的服务")
@RestController
@RequestMapping("${api.version}/publish")
public class TimeSeriesServiceController {

    public static final Log log = LogFactory.get();

    private final DataSetService dataSetService;

    private final StationService stationService;

    public TimeSeriesServiceController(DataSetService dataSetService, StationService stationService) {
        this.dataSetService = dataSetService;
        this.stationService = stationService;
    }


    @ApiOperation(value = "根据时序视图的名称，获取视图中测点的时序数据，可通过searchCondition过滤测点，通过filterExp加filterValue过滤测点的时序数值")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "method", value = "获取数据的方式，1或者空为默认方式返回json数据，0为流式查询，返回流式字符数据", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetName", value = "数据视图名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetSimpleName", value = "数据视图简称，视图名称为空的情况下，简称和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetCode", value = "数据视图编码，视图名称为空的情况下，编码和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetStatus", value = "数据视图状态，视图名称为空的情况下，简称和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attrInfoString", value = "查询结果保留字段,填写英文，多项用英文逗号隔开，不要有空格。字段非必填，如果为空返回所有的项；否则返回数据中只有指定的项。目前支持stationNumber,stationName,siteId,siteName,physicsPath,bcsCHName,bcsEnName,kksCode,devicePath,abbrClassify,stationGlobalNumber,deviceName", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "searchCondition", value = "数据搜索条件，JSON字符串，按照[{\"siteName\": \"XXX\"}]的形式传入，XXX如果有多个请用英文逗号隔开，单个JSON对象内按照AND条件过滤，多个JSON对象按照OR条件过滤，目前支持的数组元素的key包括siteName（厂站名称），stationNumber（测点原始编码），stationName（测点原始名称），deviceName（设备名称），kkscode（设备KKS），logicPath（设备逻辑路径），physicsDevicePath（设备物理路径），attrClassify（属性分类），enName（BCS英文名），cnName（BCS中文名），globalNumber（设备全局编码）", dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "stime", value = "开始时间，YYYY-MM-DD HH:MM:SS格式", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "etime", value = "结束时间，YYYY-MM-DD HH:MM:SS格式", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNumber", value = "时序数据的分页下标，从0开始，只对时间段数据有效，采样和对齐模式下无效", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "时序数据返回数据大小，只对时间段数据有效，如果传入负值将等间隔返回数据", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "timeWindows", value = "窗口模式时间参数，按照多个开始时间~结束时间（中间用波浪号分隔），英文逗号分隔的方式传入，例如2022-01-01 00:00:00~2022-01-01 01:00:00,2022-01-02 00:00:00~2022-01-02 01:00:00", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "stableModel", value = "是否为稳定工况模式, 1为是，0或者空为否，默认为否", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "datasource", value = "数据源，0为时序仓库，1为时序库", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "filterExp", value = "时序值的检索条件 参数要么为空，要么为=，>=，>，<，<=", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "filterValue", value = "时序值的检索值,如果filterExp有值，则filterValue一定要有值", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "aligned", value = "对齐步长（单位：毫秒），和采样模式互斥，采样优先", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sampled", value = "采样步长（单位：秒），按以下两种情况设定值： sampled>0 - 按该采样步长间隔稀疏值返回 sampled=0 - 不进行采样。注意，此参数不支持协同传输的时序视图。", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "resultType", value = "采样返回值类型，默认值为RT_FIRST，可选值： RT_FIRST - 返回大于时间段开始时间的第一个的点值 RT_LAST - 返回小于时间段开始时间的第一个的点值 RT_MAX - 返回时间段最大值 RT_MIN - 返回时间段最小值。注意，此参数不支持协同传输的时序视图。", dataType = "String", paramType = "query", defaultValue = "RT_FIRST"),
            @ApiImplicitParam(name = "fillPoint", value = "补值策略：1: 线性补值, 0: 前值补值, -1：不补值。注意，此参数不支持协同传输的时序视图。", dataType = "String", paramType = "query", defaultValue = "-1"),
            @ApiImplicitParam(name = "filterQ", value = "质量位筛选条件，非数值字符串表示不筛选", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isStats", value = "时序数据是否返回最大、最小、平均统计值，1为是，0为否，默认为否", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "timingSwitch", value = "是否查询时序值 0为是 1为否 默认为是", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "测点查询结果分页参数，当前页数", required = true, dataType = "Int", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "limit", value = "测点查询结果分页参数，每页数据条数", required = true, dataType = "Int", paramType = "query", example = "10")
    })
    @MethodOpenApi
    @PostMapping("/dataSet/getEncryTimeSeriesByDataSet")
    public ResponseEntity<StreamingResponseBody> getEncryTimeSeriesByDataSet(HttpServletRequest request, String method, String dataSetName, String dataSetSimpleName, String dataSetCode, String dataSetStatus, String attrInfoString, @RequestBody(required = false) String searchCondition, String stime, String etime, Integer pageNumber, Integer pageSize, String timeWindows, String stableModel, String datasource, String filterExp, String filterValue, Integer page, Integer limit, String aligned, String sampled, String resultType, String fillPoint, String filterQ, String isStats,Integer timingSwitch) {
        String userId = AvatarThreadContext.userId.get();
        String ipAdrress = BaseUtil.getIpAdrress(request);
        String ekey;
        try{
             ekey = SM4Utils.generateBase64StringKey();
        }catch (Exception e){
            throw new BusinessException("生成密钥失败");
        }
        String finalEkey = ekey;
        StreamingResponseBody responseBody = cipherOut -> {
            try {
                ByteArrayOutputStream captureStream = new ByteArrayOutputStream();
                log.info("开始查询测点时序数据.............");
                if (StrUtil.isNotBlank(method) && !StrUtil.equalsAny(method, "0", "1")) {
                    throw new BusinessException("获取数据的方式只能为空，或者0和1中的一种");
                }
                if (StrUtil.isAllBlank(dataSetName, dataSetSimpleName, dataSetCode) || StrUtil.isAllBlank(dataSetName, dataSetStatus)) {
                    throw new BusinessException("视图名称为空时，视图简称和视图状态必须输入");
                }
                if (StrUtil.isAllNotBlank(stime, etime, timeWindows) || StrUtil.isAllNotBlank(stime, timeWindows) || StrUtil.isAllNotBlank(etime, timeWindows)) {
                    throw new BusinessException("开始时间，结束时间和窗口模式时间参数不能同时输入");
                }
                if (StrUtil.equals(stableModel, "1")) {
                    if (StrUtil.equalsAny(method, "0")) {
                        throw new BusinessException("稳定工况模式不支持流式查询");
                    }
                    if (StrUtil.isAllBlank(stime, etime)) {
                        throw new BusinessException("稳定工况模式下必须输入开始时间和结束时间");
                    }
                }
                if (StrUtil.equals(method, "0") && StrUtil.isNotBlank(aligned)) {
                    throw new BusinessException("流式查询不支持对齐步长参数aligned");
                }
                if (StrUtil.isNotBlank(timeWindows)) {
                    if (StrUtil.equalsAny(method, "0")) {
                        throw new BusinessException("窗口模式不支持流式查询");
                    }
                    List<String> timeWindowsList = StrUtil.split(timeWindows, ",");
                    for (String item : timeWindowsList) {
                        List<String> itemList = StrUtil.split(item, "~");
                        if (itemList.size() != 2) {
                            throw new BusinessException("窗口模式时间参数，按照多个开始时间~结束时间（开始结束时间必须输入，中间用波浪号分隔），英文逗号分隔的方式传入");
                        }
                    }
                }
                if (StrUtil.equals(method, "0") && StrUtil.isNotBlank(attrInfoString)) {
                    throw new BusinessException("流式查询不支持参数attrInfoString");
                }
                AvatarThreadContext.userId.set(userId);
                String decodeFilterExp = URLUtil.decode(filterExp);
                String token = BaseUtil.getTokenFromHeader(request);

                if (StrUtil.equals(method, "0")) {
                    dataSetService.getTimeSeriesStreamByDataSet(captureStream, ipAdrress, token, dataSetName, dataSetSimpleName, dataSetCode, dataSetStatus, searchCondition, stime, etime, page, limit, sampled, resultType, fillPoint, filterQ, isStats);
                } else {
                    Map<String, Object> rsMap = dataSetService.getTimeSeriesByDataSet(ipAdrress, token, dataSetName, dataSetSimpleName, dataSetCode, dataSetStatus, attrInfoString, searchCondition, stime, etime, pageNumber, pageSize, timeWindows, stableModel, datasource, decodeFilterExp, filterValue, page, limit, aligned, sampled, resultType, fillPoint, filterQ, isStats, timingSwitch);
                    AvatarThreadContext.userId.remove();
                    captureStream.write(JSONUtil.parseObj(Objects.requireNonNull(Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("data", rsMap.get("rsList"))).put("total", rsMap.get("total"))).put("rtCompMax", rsMap.get("rtCompMax"))).put("rtCompMin", rsMap.get("rtCompMin"))).toString().getBytes(StandardCharsets.UTF_8));
                }

                byte[] originalData = captureStream.toByteArray(); // 获取原始数据
                String originalStr = new String(originalData, StandardCharsets.UTF_8);
                String encryptedData = SM4Utils.encrypt(finalEkey, originalStr); // 加密

                cipherOut.write(encryptedData.getBytes(StandardCharsets.UTF_8));
                log.info("测点时序数据获取完毕");
            } catch (Exception e) {
                cipherOut.write(JSONUtil.parseObj(JsonResult.error(e.getMessage())).toString().getBytes(StandardCharsets.UTF_8));
            } finally {
                cipherOut.flush();
                cipherOut.close();
            }
        };
        return ResponseEntity.ok()
                .header("Content-Type", "application/octet-stream")
                .header("x-key", finalEkey)
                .body(responseBody);

    }





    @ApiOperation(value = "根据时序视图的名称，获取视图中测点的时序数据，可通过searchCondition过滤测点，通过filterExp加filterValue过滤测点的时序数值")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "method", value = "获取数据的方式，1或者空为默认方式返回json数据，0为流式查询，返回流式字符数据", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetName", value = "数据视图名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetSimpleName", value = "数据视图简称，视图名称为空的情况下，简称和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetCode", value = "数据视图编码，视图名称为空的情况下，编码和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetStatus", value = "数据视图状态，视图名称为空的情况下，简称和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attrInfoString", value = "查询结果保留字段,填写英文，多项用英文逗号隔开，不要有空格。字段非必填，如果为空返回所有的项；否则返回数据中只有指定的项。目前支持stationNumber,stationName,siteId,siteName,physicsPath,bcsCHName,bcsEnName,kksCode,devicePath,abbrClassify,stationGlobalNumber,deviceName", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "searchCondition", value = "数据搜索条件，JSON字符串，按照[{\"siteName\": \"XXX\"}]的形式传入，XXX如果有多个请用英文逗号隔开，单个JSON对象内按照AND条件过滤，多个JSON对象按照OR条件过滤，目前支持的数组元素的key包括siteName（厂站名称），stationNumber（测点原始编码），stationName（测点原始名称），deviceName（设备名称），kkscode（设备KKS），logicPath（设备逻辑路径），physicsDevicePath（设备物理路径），attrClassify（属性分类），enName（BCS英文名），cnName（BCS中文名），globalNumber（设备全局编码）", dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "stime", value = "开始时间，YYYY-MM-DD HH:MM:SS格式", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "etime", value = "结束时间，YYYY-MM-DD HH:MM:SS格式", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNumber", value = "时序数据的分页下标，从0开始，只对时间段数据有效，采样和对齐模式下无效", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "时序数据返回数据大小，只对时间段数据有效，如果传入负值将等间隔返回数据", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "timeWindows", value = "窗口模式时间参数，按照多个开始时间~结束时间（中间用波浪号分隔），英文逗号分隔的方式传入，例如2022-01-01 00:00:00~2022-01-01 01:00:00,2022-01-02 00:00:00~2022-01-02 01:00:00", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "stableModel", value = "是否为稳定工况模式, 1为是，0或者空为否，默认为否", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "datasource", value = "数据源，0为时序仓库，1为时序库", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "filterExp", value = "时序值的检索条件 参数要么为空，要么为=，>=，>，<，<=", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "filterValue", value = "时序值的检索值,如果filterExp有值，则filterValue一定要有值", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "aligned", value = "对齐步长（单位：毫秒），和采样模式互斥，采样优先", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sampled", value = "采样步长（单位：秒），按以下两种情况设定值： sampled>0 - 按该采样步长间隔稀疏值返回 sampled=0 - 不进行采样。注意，此参数不支持协同传输的时序视图。", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "resultType", value = "采样返回值类型，默认值为RT_FIRST，可选值： RT_FIRST - 返回大于时间段开始时间的第一个的点值 RT_LAST - 返回小于时间段开始时间的第一个的点值 RT_MAX - 返回时间段最大值 RT_MIN - 返回时间段最小值。注意，此参数不支持协同传输的时序视图。", dataType = "String", paramType = "query", defaultValue = "RT_FIRST"),
            @ApiImplicitParam(name = "fillPoint", value = "补值策略：1: 线性补值, 0: 前值补值, -1：不补值。注意，此参数不支持协同传输的时序视图。", dataType = "String", paramType = "query", defaultValue = "-1"),
            @ApiImplicitParam(name = "filterQ", value = "质量位筛选条件，非数值字符串表示不筛选", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isStats", value = "时序数据是否返回最大、最小、平均统计值，1为是，0为否，默认为否", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "timingSwitch", value = "是否查询时序值 0为是 1为否 默认为是", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "测点查询结果分页参数，当前页数", required = true, dataType = "Int", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "limit", value = "测点查询结果分页参数，每页数据条数", required = true, dataType = "Int", paramType = "query", example = "10")
    })
    @MethodOpenApi
    @PostMapping("/dataSet/getTimeSeriesByDataSet")
    public ResponseEntity<StreamingResponseBody> getTimeSeriesByDataSet(HttpServletRequest request, String method, String dataSetName, String dataSetSimpleName, String dataSetCode, String dataSetStatus, String attrInfoString, @RequestBody(required = false) String searchCondition, String stime, String etime, Integer pageNumber, Integer pageSize, String timeWindows, String stableModel, String datasource, String filterExp, String filterValue, Integer page, Integer limit, String aligned, String sampled, String resultType, String fillPoint, String filterQ, String isStats,Integer timingSwitch) {
        String userId = AvatarThreadContext.userId.get();
        String ipAdrress = BaseUtil.getIpAdrress(request);
        StreamingResponseBody responseBody = outputStream -> {
            try {
                log.info("开始查询测点时序数据.............");
                if (StrUtil.isNotBlank(method) && !StrUtil.equalsAny(method, "0", "1")) {
                    throw new BusinessException("获取数据的方式只能为空，或者0和1中的一种");
                }
                if (StrUtil.isAllBlank(dataSetName, dataSetSimpleName, dataSetCode) || StrUtil.isAllBlank(dataSetName, dataSetStatus)) {
                    throw new BusinessException("视图名称为空时，视图简称和视图状态必须输入");
                }
                if (StrUtil.isAllNotBlank(stime, etime, timeWindows) || StrUtil.isAllNotBlank(stime, timeWindows) || StrUtil.isAllNotBlank(etime, timeWindows)) {
                    throw new BusinessException("开始时间，结束时间和窗口模式时间参数不能同时输入");
                }
                if (StrUtil.equals(stableModel, "1")) {
                    if (StrUtil.equalsAny(method, "0")) {
                        throw new BusinessException("稳定工况模式不支持流式查询");
                    }
                    if (StrUtil.isAllBlank(stime, etime)) {
                        throw new BusinessException("稳定工况模式下必须输入开始时间和结束时间");
                    }
                }
                if (StrUtil.equals(method, "0") && StrUtil.isNotBlank(aligned)) {
                    throw new BusinessException("流式查询不支持对齐步长参数aligned");
                }
                if (StrUtil.isNotBlank(timeWindows)) {
                    if (StrUtil.equalsAny(method, "0")) {
                        throw new BusinessException("窗口模式不支持流式查询");
                    }
                    List<String> timeWindowsList = StrUtil.split(timeWindows, ",");
                    for (String item : timeWindowsList) {
                        List<String> itemList = StrUtil.split(item, "~");
                        if (itemList.size() != 2) {
                            throw new BusinessException("窗口模式时间参数，按照多个开始时间~结束时间（开始结束时间必须输入，中间用波浪号分隔），英文逗号分隔的方式传入");
                        }
                    }
                }
                if (StrUtil.equals(method, "0") && StrUtil.isNotBlank(attrInfoString)) {
                    throw new BusinessException("流式查询不支持参数attrInfoString");
                }
                AvatarThreadContext.userId.set(userId);
                String decodeFilterExp = URLUtil.decode(filterExp);
                String token = BaseUtil.getTokenFromHeader(request);

                if (StrUtil.equals(method, "0")) {
                    dataSetService.getTimeSeriesStreamByDataSet(outputStream, ipAdrress, token, dataSetName, dataSetSimpleName, dataSetCode, dataSetStatus, searchCondition, stime, etime, page, limit, sampled, resultType, fillPoint, filterQ, isStats);
                } else {
                    Map<String, Object> rsMap = dataSetService.getTimeSeriesByDataSet(ipAdrress, token, dataSetName, dataSetSimpleName, dataSetCode, dataSetStatus, attrInfoString, searchCondition, stime, etime, pageNumber, pageSize, timeWindows, stableModel, datasource, decodeFilterExp, filterValue, page, limit, aligned, sampled, resultType, fillPoint, filterQ, isStats,timingSwitch);
                    AvatarThreadContext.userId.remove();
                    outputStream.write(JSONUtil.parseObj(Objects.requireNonNull(Objects.requireNonNull(Objects.requireNonNull(JsonResult.ok().put("data", rsMap.get("rsList"))).put("total", rsMap.get("total"))).put("rtCompMax", rsMap.get("rtCompMax"))).put("rtCompMin", rsMap.get("rtCompMin"))).toString().getBytes(StandardCharsets.UTF_8));
                }
                log.info("测点时序数据获取完毕");
            } catch (Exception e) {
                outputStream.write(JSONUtil.parseObj(JsonResult.error(e.getMessage())).toString().getBytes(StandardCharsets.UTF_8));
            } finally {
                outputStream.flush();
                outputStream.close();
            }
        };
        return ResponseEntity.ok().header("Content-Type", "application/json").body(responseBody);
    }

    @ApiOperation(value = "获取测点时序数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stationSelectList", value = "所选择的测点列表，json格式, [\n" +
                    "    {\n" +
                    "        \"stationNumber\": \"测点编号\", \n" +
                    "        \"siteId\": \"厂站ID\", \n" +
                    "        \"siteName\": \"厂站名称\", \n" +
                    "        \"stationName\": \"测点名称\", \n" +
                    "    }\n" +
                    "]", required = true, dataType = "String", paramType = "body"),
            @ApiImplicitParam(name = "stime", value = "开始时间，YYYY-MM-DD HH:MM:SS格式", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "etime", value = "结束时间，YYYY-MM-DD HH:MM:SS格式", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNumber", value = "分页下标，从0开始，只对时间段数据有效，采样和对齐模式下无效", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "返回数据大小，只对时间段数据有效，如果传入负值将等间隔返回数据", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "datasource", value = "数据源，0为时序仓库，1为时序库", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "aligned", value = "对齐步长（单位：毫秒），和采样模式互斥，采样优先", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "sampled", value = "采样步长（单位：秒），按以下两种情况设定值： sampled>0 - 按该采样步长间隔稀疏值返回 sampled=0 - 不进行采样。注意，此参数不支持协同传输的时序视图。", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "resultType", value = "返回值类型，默认值为RT_ORIGINAL，可选值： RT_ORIGINAL : 返回原始值 RT_FIRST : 返回大于时间段开始时间的第一个的点值 RT_LAST : 返回小于时间段结束时间的第一个的点值 RT_MAX : 返回时间段最大值 RT_MIN : 返回时间段最小值 RT_AVG : 返回时间段平均值 RT_SUM : 返回时间段值的和 RT_MED : 返回时间段中位数 RT_PER : 返回时间段百分位数 RT_ALL : 返回时间段值最新值,最小值,平均值，最大值，中位数(对应Q字段标记:0,1,2,3,4) RT_SWITCH : 开关量优化模式，只返回变化临界点，此种模式忽略采样，对齐参数 RT_COMP_MAX : 返回时间段所有传入测点的最大值 RT_COMP_MIN : 返回时间段所有传入测点的最小值", dataType = "String", paramType = "query", defaultValue = "RT_ORIGINAL"),
            @ApiImplicitParam(name = "fillPoint", value = "补值策略：1: 线性补值, 0: 前值补值, -1：不补值。注意，此参数不支持协同传输的时序视图。", dataType = "String", paramType = "query", defaultValue = "-1"),
            @ApiImplicitParam(name = "filterQ", value = "质量位筛选条件，非数值字符串表示不筛选", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isStats", value = "时序数据是否返回最大、最小、平均统计值，1为是，0为否，默认为否", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "exceptionType ", value = "过滤异常类型,默认为空 1: 中断异常,2: 跳变异常,3: 死值异常,4: 超上限异常,5: 超下限异常,6: 无效值异常 多个用逗号分隔", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "returnData ", value = "返回数据类型,默认为空返回时序值,1则代表不返回时序值数据直接返回各测点正常值时间区间(仅在exceptionType不为空时生效)", dataType = "String", paramType = "query")

    })
    @MethodOpenApi
    @PostMapping("/station/getTimeSeries")
    public ResponseEntity<StreamingResponseBody> getStationTimeSeriesData(HttpServletRequest request, @RequestBody String stationSelectList, String stime, String etime, Integer pageNumber, Integer pageSize, String datasource, String aligned, String sampled, String resultType, String fillPoint, String filterQ, String isStats,String exceptionType,String returnData) {
        StreamingResponseBody responseBody = outputStream -> {
            try {
                log.info("开始查询测点时序数据.............");
                String token = BaseUtil.getTokenFromHeader(request);
                Map<String, Object> rsMap = stationService.getTimeSeriesData(token, null, stationSelectList, stime, etime, pageNumber, pageSize, datasource, aligned, sampled, resultType, fillPoint, filterQ, isStats, exceptionType,returnData);
                log.info("测点时序数据获取完毕");
                outputStream.write(JSONUtil.parseObj(Objects.requireNonNull(JsonResult.ok().put("data", rsMap.get("rsList")))).toString().getBytes(StandardCharsets.UTF_8));
            } catch (Exception e) {
                outputStream.write(JSONUtil.parseObj(JsonResult.error(e.getMessage())).toString().getBytes(StandardCharsets.UTF_8));
            } finally {
                outputStream.flush();
                outputStream.close();
            }
        };
        return ResponseEntity.ok().header("Content-Type", "application/json").body(responseBody);
    }

    @ApiOperation(value = "获取时序最新数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stationNumber", value = "测点编号", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "siteId", value = "厂站id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "datasource", value = "数据源：0为时序仓库，1为时序库", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "filterQ", value = "质量位筛选条件，非数值字符串表示不筛选", dataType = "String", paramType = "query")
    })
    @MethodOpenApi
    @GetMapping("/station/getLastTimeSeries")
    public Map<String, Object> getLastTimeSeriesData(HttpServletRequest request, String stationNumber, String siteId, String datasource, String filterQ) {
        log.info("开始查询测点时序数据.............");
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = stationService.getLastTimeSeriesData(token, stationNumber, siteId, datasource, filterQ);
        log.info("测点时序数据获取完毕");
        return JsonResult.ok().put("data", rsMap.get("rsList"));
    }
}
