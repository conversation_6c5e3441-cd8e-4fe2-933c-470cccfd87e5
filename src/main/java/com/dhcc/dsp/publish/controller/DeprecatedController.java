package com.dhcc.dsp.publish.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.dhcc.avatar.aspect.DisableWrite;
import com.dhcc.avatar.aspect.HasWriteRight;
import com.dhcc.avatar.common.MethodOpenApi;
import com.dhcc.dsp.business.service.DataSetService;
import com.dhcc.dsp.business.service.StationService;
import com.dhcc.dsp.business.service.StructDataService;
import com.dhcc.dsp.common.JsonResult;
import com.dhcc.dsp.common.utils.BaseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Objects;

@Api(value = "已经过时的数据服务的接口，不维护新功能", tags = "dataService")
@RestController
@ApiIgnore
@RequestMapping("${api.version}/publish")
public class DeprecatedController {

    private static final Log log = LogFactory.get();

    private final StructDataService structDataService;

    private final StationService stationService;

    private final DataSetService dataSetService;

    public DeprecatedController(StructDataService structDataService, StationService stationService, DataSetService dataSetService) {
        this.structDataService = structDataService;
        this.stationService = stationService;
        this.dataSetService = dataSetService;
    }

    @ApiOperation(value = "获取结构化数据具体数据", notes = "")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataObjname", value = "结构化数据对象名称,对应数据视图查询中返回的path字段", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attrInfoString", value = "字段名称,对应属性列表中要取得的字段名称,为空全出", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "searchCondition", value = "结构化搜索条件，查询的过滤条件，此处可以填写sql查询的条件。示例 字段= '筛选值' and 字段2 != '筛选值' ", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderBy", value = "结构化排序条件，示例  字段 desc/asc", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "结果分页参数，当前页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "结果分页参数，每页条数", required = true, dataType = "String", paramType = "query")
    })
    @MethodOpenApi
    @GetMapping("/structData/getStructDataDetail")
    public Map<String, Object> getStructDataDetail(HttpServletRequest request, String dataObjname, String attrInfoString, String searchCondition, String orderBy, int page, int limit) {
        log.info("开始调用系统服务获取结构化数据具体数据.............");
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = structDataService.getStructDetail(token, dataObjname, attrInfoString, searchCondition, orderBy, page, limit);
        log.info("结构化数据具体数据获取完毕");
        return Objects.requireNonNull(JsonResult.ok().put("totalCount", rsMap.get("totalCount"))).put("data", rsMap.get("rsList"));
    }

    @ApiOperation(value = "下载结构化数据具体数据文件", notes = "")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataObjname", value = "结构化数据对象名称,对应数据视图查询中返回的path字段", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "attrInfoString", value = "字段名称,对应属性列表中要取得的字段名称,为空全出", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "searchCondition", value = "结构化搜索条件，查询的过滤条件，此处可以填写sql查询的条件。示例 字段= '筛选值' and 字段2 != '筛选值' ", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderBy", value = "结构化排序条件，示例  字段 desc/asc", required = false, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "结果分页参数，当前页数", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "结果分页参数，每页条数", required = true, dataType = "String", paramType = "query")
    })
    @MethodOpenApi
    @GetMapping("/structData/getStructDataDetailFile")
    public void getStructDataDetailFile(HttpServletRequest request, HttpServletResponse response, String dataObjname, String attrInfoString, String searchCondition, String orderBy, int page, int limit) {
        log.info("开始调用系统服务生成结构化数据具体数据下载文件.............");
        String token = BaseUtil.getTokenFromHeader(request);
        structDataService.getStructDataDetailFile(response, token, dataObjname, attrInfoString, searchCondition, orderBy, page, limit);
        log.info("下载结构化数据具体数据文件完成");
    }

    @DisableWrite
    @HasWriteRight("当前用户没有修改权限")
    @ApiOperation(value = "修改结构化数据具体数据", notes = "")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataObjname", value = "结构化对象名称", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "updateExpression", value = "更新的条件  示例[{\"映射字段\":\"修改值\",\"映射字段\":\"修改值\"},{\"映射字段\":\"修改值\",\"映射字段\":\"修改值\"}] ", required = true, dataType = "String", paramType = "query")
    })
    @MethodOpenApi
    @PostMapping("/structData/updateStructDataDetail")
    public Map<String, Object> updateStructDataDetail(HttpServletRequest request, String dataObjname, String updateExpression) {
        log.info("开始调用系统服务更新结构化数据具体数据.............");
        String token = BaseUtil.getTokenFromHeader(request);
        Map<String, Object> rsMap = structDataService.updateStructDataDetail(token, dataObjname, updateExpression);
        log.info("结构化数据具体数据更新完毕");
        return JsonResult.ok().put("repMessage", rsMap.get("repMessage"));
    }

    @GetMapping("/dataSet/getTimeSeriesByDataSet")
    public JsonResult getTimeSeriesByDataSet(HttpServletRequest request, String dataSetName, String attrInfoString, String searchCondition, String stime, String etime, String datasource, String filterExp, String filterValue, Integer page, Integer limit, String sampled, String resultType, String fillPoint) {
//        StreamingResponseBody responseBody = outputStream -> {
//            log.info("开始查询测点时序数据.............");
//            String decodeFilterExp = URLUtil.decode(filterExp);
//            String token = BaseUtil.getTokenFromHeader(request);
//            Map<String, Object> rsMap = dataSetService.getTimeSeriesByDataSet(BaseUtil.getIpAdrress(request), token, dataSetName, "", "", "0", attrInfoString, searchCondition, stime, etime, "", "", datasource, decodeFilterExp, filterValue, page, limit, "", sampled, resultType, fillPoint, "0", "");
//            log.info("测点时序数据获取完毕");
//            outputStream.write(JSONUtil.parseObj(JsonResult.ok().put("data", rsMap.get("rsList")).put("total", rsMap.get("total"))).toString().getBytes(StandardCharsets.UTF_8));
//        };
        return JsonResult.error("请使用POST方法调用此接口");
    }

//    @ApiOperation(value = "获取测点时序数据")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "stationSelectList", value = "所选择的测点列表，json格式, [\n" +
//                    "    {\n" +
//                    "        \"stationNumber\": \"测点编号\", \n" +
//                    "        \"siteId\": \"厂站ID\", \n" +
//                    "        \"siteName\": \"厂站名称\", \n" +
//                    "        \"stationName\": \"测点名称\", \n" +
//                    "    }\n" +
//                    "]", required = true, dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "stime", value = "开始时间，YYYY-MM-DD HH:MM:SS格式", required = true, dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "etime", value = "结束时间，YYYY-MM-DD HH:MM:SS格式", required = true, dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "datasource", value = "数据源，0为时序仓库，1为时序库", required = true, dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "sampled", value = "采样步长（单位：秒），0代表不采样", dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "resultType", value = "采样返回值类型，默认值为RT_FIRST，可选值： RT_FIRST - 返回大于时间段开始时间的第一个的点值 RT_LAST - 返回小于时间段开始时间的第一个的点值 RT_MAX - 返回时间段最大值 RT_MIN - 返回时间段最小值", dataType = "String", paramType = "query", defaultValue = "RT_FIRST"),
//            @ApiImplicitParam(name = "fillPoint", value = "补值策略：0: 前值补值, -1：不补值 默认-1", dataType = "String", paramType = "query", defaultValue = "-1"),
//            @ApiImplicitParam(name = "filterQ", value = "质量位筛选条件，非数值字符串表示不筛选", dataType = "String", paramType = "query"),
//            @ApiImplicitParam(name = "isStats", value = "时序数据是否返回最大、最小、平均统计值，1为是，0为否，默认为否", dataType = "String", paramType = "query")
//    })
//    @MethodOpenApi
//    @GetMapping("/station/getTimeSeries")
//    public Map<String, Object> getTimeSeriesData(HttpServletRequest request, String stationSelectList, String stime, String etime, String datasource, String sampled, String resultType, String blockData, String fillPoint, String filterQ, String isStats) {
//        log.info("开始查询测点时序数据.............");
//        stationSelectList = URLUtil.decode(stationSelectList);
//        String token = BaseUtil.getTokenFromHeader(request);
//        Map<String, Object> rsMap = stationService.getTimeSeriesData(token, null, stationSelectList, stime, etime, null, null, datasource, "", sampled, resultType, fillPoint, filterQ, isStats,"","");
//        log.info("测点时序数据获取完毕");
//        return JsonResult.ok().put("data", rsMap.get("rsList"));
//    }

    @ApiOperation(value = "根据数据视图的名称，查询数据视图的明细，对于时序视图，返回测点信息；对于结构化视图，返回字段信息；对于非结构化视图，返回文件元信息；对于维度视图，返回列信息，对于方法视图，返回方法列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dataSetName", value = "数据视图名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetSimpleName", value = "数据视图简称，视图名称为空的情况下，简称和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetCode", value = "数据视图编码，视图名称为空的情况下，编码和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "dataSetStatus", value = "数据视图状态，视图名称为空的情况下，简称和状态必须输入", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "searchCondition", value = "数据搜索条件，只对时序视图有效，按照[{siteName: 'XXX'}]的形式传入，目前支持的数组元素的key包括siteName（厂站名称），stationNumber（测点原始编码），stationName（测点原始名称），deviceName（设备名称），kkscode（设备KKS），logicPath（设备逻辑路径），physicsDevicePath（设备物理路径），attrClassify（属性分类），enName（BCS英文名），cnName（BCS中文名），globalNumber（设备全局编码）", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "结果分页参数，当前页数", required = true, dataType = "Int", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "limit", value = "结果分页参数，每页数据条数", required = true, dataType = "Int", paramType = "query", example = "10")
    })
    @MethodOpenApi
    @GetMapping("/dataSet/queryDetailByName")
    public Map<String, Object> queryDataSet(HttpServletRequest request, String dataSetName, String dataSetSimpleName, String dataSetCode, String dataSetStatus, String searchCondition, Integer page, Integer limit) {
        if (StrUtil.isAllBlank(dataSetName, dataSetSimpleName, dataSetCode) || StrUtil.isAllBlank(dataSetName, dataSetStatus)) {
            return JsonResult.error("视图名称为空时，视图简称和视图状态必须输入");
        }
        if (page == null || limit == null) {
            return JsonResult.error("分页参数为空。");
        }
        String token = BaseUtil.getTokenFromHeader(request);
        String ipAdrress = BaseUtil.getIpAdrress(request);
        Map<String, Object> rsMap = dataSetService.getDataSetDetailWithVisit(ipAdrress, token, dataSetName, dataSetSimpleName, dataSetCode, dataSetStatus, "", searchCondition, page, limit);
        return Objects.requireNonNull(JsonResult.ok().put("total", rsMap.get("total"))).put("data", rsMap.get("data"));
    }
}
