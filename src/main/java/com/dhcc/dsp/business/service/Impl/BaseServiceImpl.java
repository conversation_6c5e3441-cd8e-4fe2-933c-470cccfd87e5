package com.dhcc.dsp.business.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.dhcc.avatar.common.SystemConstants;
import com.dhcc.avatar.domain.AvatarThreadContext;
import com.dhcc.avatar.domain.sys.Domain;
import com.dhcc.avatar.util.EncryptUtil;
import com.dhcc.avatar.util.UUIDUtil;
import com.dhcc.dsp.business.model.Station;
import com.dhcc.dsp.business.service.BaseService;
import com.dhcc.dsp.business.service.DataSetTimeSeriesService;
import com.dhcc.dsp.common.consts.AvaConsts;
import com.dhcc.dsp.common.consts.HttpConsts;
import com.dhcc.dsp.common.consts.ResourceConsts;
import com.dhcc.dsp.common.exception.BusinessException;
import com.dhcc.dsp.common.utils.BaseUtil;
import com.dhcc.dsp.system.service.SysConfigService;
import com.dhcc.dsp.system.service.SysDomainService;
import com.dhcc.dsp.system.service.impl.DataCacheService;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.MappingJsonFactory;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@DS("master")
@Service
public class BaseServiceImpl implements BaseService {

    static {
        HttpRequest.closeCookie();
    }

    private final DataCacheService dataCacheService;

    private final SysDomainService sysDomainService;

    private final SysConfigService sysConfigService;

    private final DataSetTimeSeriesService dataSetTimeSeriesService;

    private static final Log log = LogFactory.get();

    public BaseServiceImpl(DataCacheService dataCacheService, SysDomainService sysDomainService, SysConfigService sysConfigService, DataSetTimeSeriesService dataSetTimeSeriesService) {
        this.dataCacheService = dataCacheService;
        this.sysDomainService = sysDomainService;
        this.sysConfigService = sysConfigService;
        this.dataSetTimeSeriesService = dataSetTimeSeriesService;
    }

    @Override
    public List<Domain> getDomainList() {
        log.info("开始获取数据服务中配置的需要处理的业务空间数据");
        String configDomainStr = sysConfigService.getDomainList();
        if (StrUtil.isBlank(configDomainStr)) {
            log.info("当前没有配置需要处理的业务空间，取系统中的默认业务空间");
            Domain sysDomain = sysDomainService.getDefaultDomain();
            if (sysDomain == null) {
                throw new BusinessException("系统中没有配置默认业务空间");
            }
            configDomainStr = sysDomain.getDomainname();
        }
        List<String> configDomainNameList = Arrays.asList(configDomainStr.split(","));
        List<Domain> sysDomainList = sysDomainService.getDomainList(configDomainNameList);
        if (sysDomainList.size() == 0 || sysDomainList.size() != configDomainNameList.size()) {
            throw new BusinessException("配置的业务空间【{}】在系统中不存在", configDomainStr);
        }

        return sysDomainList;
    }

    @Override
    public String getAvatarToken(String userName, String password) {
        log.info("开始根据用户名和密码获取系统token");
        String url = sysConfigService.getAvatarServiceGateway();
        String param = StrUtil.format("{\"userName\":\"{}\",\"passWord\":\"{}\"}", userName, password);
        String rs;
        try {
            rs = HttpUtil.post(url + AvaConsts.LOGIN_CHECK_URL, param);
        } catch (Exception e) {
            log.error("获取系统的access_token出现异常：" + e);
            throw new BusinessException("获取系统的access_token出现异常：" + e);
        }
        log.info("调用系统获取token服务的返回：{}", rs);
        JSONObject jsonObject = JSONUtil.parseObj(rs);
        String statusCode = jsonObject.getStr("statusCode");
        String repMessage = jsonObject.getStr("repMessage");
        if (!StrUtil.equals(statusCode, "0")) {
            throw new BusinessException("用户验证失败：{}", repMessage);
        }

        return jsonObject.getStr("data");
    }

    @Override
    public String doRequest(String method, String endPoint, String accessToken, String paramStr, Map<String, Object> paramMap) {
        return doRequest(method, endPoint, accessToken, paramStr, "", paramMap);
    }

    @Override
    public String doRequest(String method, String endPoint, String accessToken, String paramStr, String contentType, Map<String, Object> paramMap) {
        return doRequest(method, endPoint, accessToken, paramStr, contentType, paramMap, null);
    }

    /**
     * 调用系统的通用方法
     * 注意：1. access_token在一般情况下，务必输入从前端传入的HttpServletRequest中的request.getHeader("Authorization")值
     * 2. 只有对于publish下的control，如果需要调用系统的服务，access_token可以为空，此时调用的是系统的scheduleUser对应的token
     */
    @Override
    public String doRequest(String method, String endPoint, String accessToken, String paramStr, String contentType, Map<String, Object> paramMap, Integer timeout) {
        //如果token为空，那么使用系统的默认用户
        boolean isSchedulerUser = false;
        if (StrUtil.isBlank(accessToken)) {
            log.info("当前用户token为空，开始获取系统的默认用户token");
            isSchedulerUser = true;
            // 首先从redis中取数
            accessToken = dataCacheService.fetchObject("avatar_scheduler_user", String.class);
            if (StrUtil.isBlank(accessToken)) {
                //如果redis中没有数据，则调用系统的服务获取token
                Map<String, Object> defaultUserMap = sysConfigService.getAvatarDefaultUserInfo();
                accessToken = getAvatarToken(EncryptUtil.rsaDecryptOrNothing(Convert.toStr(defaultUserMap.get("userName"))), EncryptUtil.rsaDecryptOrNothing(Convert.toStr(defaultUserMap.get("password"))));
                dataCacheService.putObject("avatar_scheduler_user", accessToken, 0);
            }
        }
        //获取系统URL地址
        String url = sysConfigService.getAvatarServiceGateway();
        //拼接完整调用地址
        String queryUrl = StrUtil.format("{}{}", url, endPoint);
        if (StrUtil.isBlank(method)) {
            method = "post";
        }
        //调用结果
        InputStream inputStream;
        HttpRequest httpRequest;

        //打印调用日志信息
        showRequestInfo(queryUrl, accessToken, paramStr, paramMap);

        try {
            if (!StrUtil.isBlank(paramStr) && paramMap.size() == 0) {
                if (StrUtil.isNotBlank(contentType)) {
                    httpRequest = HttpRequest.post(queryUrl).header("authorization", accessToken).body(paramStr, contentType);
                } else {
                    httpRequest = HttpRequest.post(queryUrl).header("authorization", accessToken).body(paramStr);
                }
            } else if (StrUtil.isBlank(paramStr) && paramMap.size() > 0) {
                //get模式下只有form或者空
                if (StrUtil.equalsIgnoreCase(method, HttpConsts.METHOD_POST)) {
                    String uploadName = "";
                    if (paramMap.containsKey("uploadFile")) {
                        uploadName = "uploadFile";
                    }
                    if (paramMap.containsKey("schemaFile")) {
                        uploadName = "schemaFile";
                    }
                    if (paramMap.containsKey("unSchemaFile")) {
                        uploadName = "unSchemaFile";
                    }
                    if (StrUtil.isNotBlank(uploadName)) {
                        Map<String, Object> map = new HashMap<>();
                        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                            if (!StrUtil.equals(entry.getKey(), "unSchemaFile") && !StrUtil.equals(entry.getKey(), "uploadFile") && !StrUtil.equals(entry.getKey(), "schemaFile")) {
                                map.put(entry.getKey(), entry.getValue());
                            }
                        }
                        if (StrUtil.equals(uploadName, "schemaFile")) {
                            httpRequest = HttpRequest.post(queryUrl).header("authorization", accessToken).form(uploadName, paramMap.get(uploadName)).form(map);
                        } else if (StrUtil.equals(uploadName, "unSchemaFile")) {
                            httpRequest = HttpRequest.post(queryUrl).header("authorization", accessToken).form("schemaFile", BaseUtil.toFileByFixedName((MultipartFile) paramMap.get(uploadName), sysConfigService.getFileBlacklist())).form(map);

                        } else {
                            httpRequest = HttpRequest.post(queryUrl).header("authorization", accessToken).form(uploadName, BaseUtil.toFile((MultipartFile) paramMap.get(uploadName), sysConfigService.getFileBlacklist())).form(map);
                        }

                    } else {
                        httpRequest = HttpRequest.post(queryUrl).header("authorization", accessToken).form(paramMap).timeout(120000);
                    }
                } else if (StrUtil.equalsIgnoreCase(method, HttpConsts.METHOD_GET)) {
                    httpRequest = HttpRequest.get(queryUrl).header("authorization", accessToken).form(paramMap);
                } else if (StrUtil.equalsIgnoreCase(method, HttpConsts.METHOD_DELETE)) {
                    httpRequest = HttpRequest.delete(queryUrl).header("authorization", accessToken).form(paramMap);
                } else {
                    throw new BusinessException("不支持的类型：{}", method);
                }
            } else if (!StrUtil.isBlank(paramStr) && paramMap.size() > 0) {
                httpRequest = HttpRequest.post(queryUrl).header("authorization", accessToken).form(paramMap).body(paramStr);
            } else {
                //get模式下只有form或者空
                if (StrUtil.equalsIgnoreCase(method, HttpConsts.METHOD_POST)) {
                    httpRequest = HttpRequest.post(queryUrl).header("authorization", accessToken);
                } else if (StrUtil.equalsIgnoreCase(method, HttpConsts.METHOD_GET)) {
                    httpRequest = HttpRequest.get(queryUrl).header("authorization", accessToken);
                } else if (StrUtil.equalsIgnoreCase(method, HttpConsts.METHOD_DELETE)) {
                    httpRequest = HttpRequest.delete(queryUrl).header("authorization", accessToken);
                } else {
                    throw new BusinessException("不支持的类型：{}", method);
                }
            }

            httpRequest = httpRequest.header(SystemConstants.SYSTEM_CODE_HEADER, SystemConstants.WEB_CONTEXT_ROOT)
                    .header(SystemConstants.TENANT_HEADER, AvatarThreadContext.tenantSchema.get());

            if (timeout != null) {
                inputStream = httpRequest.timeout(timeout).execute().bodyStream();
            } else {
                inputStream = httpRequest.execute().bodyStream();
            }
        } catch (Exception e) {
            log.error("调用系统服务【" + queryUrl + "】【" + accessToken + "】【" + paramStr + "】【" + paramMap + "】出现异常：{}", ExceptionUtil.stacktraceToString(e));
            throw new BusinessException("调用系统服务【" + queryUrl + "】【" + accessToken + "】【" + paramStr + "】【" + paramMap + "】出现异常：{}", e.getMessage());
        }

        String result = IoUtil.read(inputStream, StandardCharsets.UTF_8);
        IoUtil.close(inputStream);
        //打印返回日志信息
        showResponseInfo(result);

        String statusCode = null;
        StringReader reader = null;
        try {
            reader = new StringReader(result);
            JsonFactory jsonFactory = new MappingJsonFactory();
            JsonParser jsonParser = jsonFactory.createParser(reader);
            while (!jsonParser.isClosed()) {
                JsonToken jsonToken = jsonParser.nextToken();
                if (JsonToken.FIELD_NAME.equals(jsonToken)) {
                    String fieldName = jsonParser.getCurrentName();
                    // 这里只获取statusCode的值，如果statusCode为-88888，需要特殊处理
                    if (StrUtil.equals(fieldName, "statusCode")) {
                        jsonParser.nextToken();
                        statusCode = jsonParser.getValueAsString();
                    }
                }
            }
        } catch (Exception e) {
            throw new BusinessException(StrUtil.format("调用服务{}返回的结果{}.......无法解析", endPoint, StrUtil.sub(result, 0, 100)));
        } finally {
            IoUtil.close(reader);
        }

        // 判断如果statusCode为-88888，对于使用系统的默认用户，重新获取token，否则抛出401的异常
        if (StrUtil.equals(statusCode, "-88888")) {
            if (isSchedulerUser) {
                result = handleResult(statusCode, queryUrl, paramStr, paramMap);
            } else {
                throw new BusinessException(401, "当前未登录或者登陆过期，请重新登陆");
            }
        }

        return result;
    }

    @Override
    public void getFileByData(HttpServletResponse response, List<Map<String, Object>> dataList) {
        try {
            //获取结构化数据后将数据形成文件返回
            String tempPath = System.getProperty("java.io.tmpdir") + UUIDUtil.generateUUID() + ".xlsx";
            File file = new File(tempPath);
            BigExcelWriter writer = ExcelUtil.getBigWriter(file);
            // 一次性写出内容，使用默认样式，强制输出标题
            writer.write(dataList, true);
            SXSSFSheet sheet = (SXSSFSheet) writer.getSheet();
            //上面需要强转SXSSFSheet  不然没有trackAllColumnsForAutoSizing方法
            sheet.trackAllColumnsForAutoSizing();
            //列宽自适应
            writer.autoSizeColumnAll();
            //response为HttpServletResponse对象
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            //file.xls是弹出下载对话框的文件名，不能为中文
            response.setHeader("Content-Disposition", "attachment;filename=file.xlsx");
            ServletOutputStream out = response.getOutputStream();
            // 终止后删除临时文件
            file.deleteOnExit();
            writer.flush(out, true);
            //此处记得关闭输出Servlet流
            IoUtil.close(out);
        } catch (Exception e) {
            log.error("导出文件出现异常：{}", ExceptionUtil.stacktraceToString(e));
            throw new BusinessException("导出文件出现异常：{}", e.getMessage());
        }
    }

    @Override
    public void getFileByAvatar(HttpServletResponse response, String URL, String isOnLine) {
        InputStream is = null;
        OutputStream out = null;
        try {
            String accessToken = dataCacheService.fetchObject("avatar_scheduler_user", String.class);
            if (StrUtil.isBlank(accessToken)) {
                //如果redis中没有数据，则调用系统的服务获取token
                Map<String, Object> defaultUserMap = sysConfigService.getAvatarDefaultUserInfo();
                accessToken = getAvatarToken(EncryptUtil.rsaDecryptOrNothing(Convert.toStr(defaultUserMap.get("userName"))), EncryptUtil.rsaDecryptOrNothing(Convert.toStr(defaultUserMap.get("password"))));
                dataCacheService.putObject("avatar_scheduler_user", accessToken, 0);
            }
            //获取系统URL地址
            String queryUrl = StrUtil.format("{}{}", sysConfigService.getAvatarServiceGateway(), URL);
            //调用结果
            HttpResponse avatarResponse = HttpRequest.post(queryUrl).header("authorization", accessToken).body("").execute();
            is = avatarResponse.bodyStream();
            byte[] buf = new byte[1024000];
            int len;
            String fileName = avatarResponse.header("Content-Disposition");
            response.reset();
            fileName = BaseUtil.handleSpecialHeadStr(fileName);
            if (StrUtil.equals(isOnLine, "0")) { // 在线打开方式
                URL u = new URL("file:///" + fileName);
                String contentType = u.openConnection().getContentType();
                String regex = "[`~!@#$%^&*()\\+\\=\\{}|:\"?><【】\\/r\\/n]";
                Pattern pa = Pattern.compile(regex);
                Matcher ma = pa.matcher(contentType);
                if (ma.find()) {
                    contentType = ma.replaceAll("");
                }
                response.setContentType(contentType);
                response.setHeader("Content-Disposition", "inline; filename=" + fileName);
            } else { // 纯下载方式
                response.setContentType("application/x-msdownload");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            }

            out = response.getOutputStream();
            while ((len = is.read(buf)) > 0) {
                out.write(buf, 0, len);
                out.flush();
            }
        } catch (Exception e) {
            log.error("获取小文件出现异常：{}", ExceptionUtil.stacktraceToString(e));
            throw new BusinessException("获取小文件出现异常：{}", e.getMessage());
        } finally {
            IoUtil.close(is);
            IoUtil.close(out);
        }
    }

    @Override
    public Map<String, Object[][]> getStableTimeSeries(String accessToken, List<Station> stationList, String stime, String etime, String filterExp, String filterValue, String sampled, String resultType, String filterQ) {
        if (stationList.size() == 0) {
            throw new BusinessException("没有需要查询的测点");
        }
        int maxSeriesSize = sysConfigService.getMaxSeriesSize();
        if (maxSeriesSize > ResourceConsts.getDefaultMaxSeriesSize()) {
            // 系统的接口单次最多支持1000个测点的查询，因此这个参数不能大于1000
            maxSeriesSize = ResourceConsts.getDefaultMaxSeriesSize();
        }
        if (StrUtil.isBlank(stime) || StrUtil.isBlank(etime)) {
            throw new BusinessException("开始和结束时间不允许为空");
        }
        //key为机组编码，value为测点编码List
        Map<String, List<String>> stationCodeMap = new HashMap<>();
        stationList.forEach(item -> {
            String simpleName = item.getSimpleName();
            String code = ObjectUtil.isEmpty(item.getStationGlobalNumber()) ? item.getStationNumber() : item.getStationGlobalNumber();
            if (StrUtil.isNotBlank(simpleName)) {
                if (stationCodeMap.containsKey(simpleName)) {
                    List<String> stationCodeList = stationCodeMap.get(simpleName);
                    stationCodeList.add(code);
                } else {
                    stationCodeMap.put(simpleName, CollUtil.newArrayList(code));
                }
            }
        });

        long startTime = Convert.toDate(stime) == null ? 0 : Convert.toDate(stime).getTime();
        long endTime = Convert.toDate(etime) == null ? 0 : Convert.toDate(etime).getTime();

        Map<String, Object[][]> rsMap = new LinkedHashMap<>();
        for (String unitCode : stationCodeMap.keySet()) {
            List<String> stationCodeList = stationCodeMap.get(unitCode);
            List<List<String>> stationCodeGroups = CollUtil.split(stationCodeList, maxSeriesSize);
            for (List<String> stationCode : stationCodeGroups) {
                if (stationCode.size() > 0) {
                    // 系统接口的输入参数
                    HashMap<String, Object> paramMap = new HashMap<>();
                    paramMap.put("unit", unitCode);
                    paramMap.put("codes", CollUtil.join(stationCode, ","));
                    paramMap.put("startTime", startTime);
                    paramMap.put("endTime", endTime);
                    if (StrUtil.isNotBlank(filterQ)) {
                        paramMap.put("filterQ", filterQ);
                    }
                    if (StrUtil.isNotBlank(sampled)) {
                        paramMap.put("sampled", sampled);
                    }
                    if (StrUtil.isNotBlank(resultType)) {
                        paramMap.put("resultType", resultType);
                    }

                    String result = doRequest(HttpConsts.METHOD_POST, AvaConsts.GET_STABLE_TIME_SERIES_URL, accessToken, "", paramMap);
                    if (StrUtil.isBlank(result)) {
                        throw new BusinessException("系统的返回结果为空，请检查");
                    }

                    buildResultMap(rsMap, result, filterExp, filterValue);
                }
            }
        }

        return rsMap;
    }

    private void buildResultMap(Map<String, Object[][]> rsMap, String result, String filterExp, String filterValue) {
        String statusCode = null;
        String repMessage = null;
        StringReader stringReader = null;
        try {
            stringReader = new StringReader(result);
            JsonFactory factory = new JsonFactory();
            JsonParser parser = factory.createParser(stringReader);
            boolean isBasicResponseExist = false;
            while (!parser.isClosed()) {
                parser.nextToken();
                String fieldName = parser.getCurrentName();
                // 这里只获取statusCode的值，如果statusCode为-88888，需要特殊处理
                if (StrUtil.equals(fieldName, "BasicResponse")) {//处理BasicResponse部分
                    isBasicResponseExist = true;
                    while (parser.nextToken() != JsonToken.END_OBJECT) {
                        String itemKey = parser.getCurrentName();
                        if (StrUtil.equals(itemKey, "succeeded")) {
                            parser.nextToken();
                            statusCode = parser.getValueAsString();
                        } else if (StrUtil.equals(itemKey, "errors")) {
                            parser.nextToken();
                            repMessage = parser.getValueAsString();
                        }
                    }
                    if (!StrUtil.equals("1", statusCode)) {//系统请求失败，succeeded为0
                        throw new BusinessException(repMessage);
                    }
                } else if (StrUtil.equals(fieldName, "RTDataSets")) { //处理RTDataSets部分
                    while (parser.nextToken() != JsonToken.END_ARRAY) {
                        String code = null;
                        List<String> timeList = new ArrayList<>();
                        List<Object> valueList = new ArrayList<>();
                        List<Integer> qList = new ArrayList<>();
                        if (parser.getCurrentToken() == JsonToken.START_OBJECT) {
                            while (parser.nextToken() != JsonToken.END_OBJECT) {
                                String itemKey = parser.getCurrentName();
                                if (StrUtil.equals(itemKey, "code")) {
                                    parser.nextToken();
                                    code = parser.getValueAsString();
                                } else if (StrUtil.equals(itemKey, "RTDataValues")) {// 处理RTDataValues部分，RTDataValues内容是一个数组
                                    if (parser.getCurrentToken() == JsonToken.START_ARRAY) {
                                        while (parser.nextToken() != JsonToken.END_ARRAY) { // 每个数组元素都是一个对象，对象形式为{Time: XXX, Value: XXX}
                                            String time = null;
                                            String value = null;
                                            Integer q = null;
                                            if (parser.getCurrentToken() == JsonToken.START_OBJECT) {
                                                while (parser.nextToken() != JsonToken.END_OBJECT) {
                                                    String key = parser.getCurrentName();
                                                    if (StrUtil.equals(key, "Time")) {// 获取对象中的Time属性值，形式为时间戳，需要转换为时间格式字符串
                                                        parser.nextToken();
                                                        time = DateUtil.format(Convert.toDate(Convert.toLong(parser.getValueAsString())), "yyyy-MM-dd HH:mm:ss");
                                                    } else if (StrUtil.equals(key, "Value")) {// 获取对象中的Value值
                                                        parser.nextToken();
                                                        value = parser.getValueAsString();
                                                    } else if (StrUtil.equals(key, "Q")) {
                                                        parser.nextToken();
                                                        q = parser.getValueAsInt();
                                                    }
                                                }
                                            }
                                            if (validate(value, filterExp, filterValue)) {// 如果有过滤参数，需要先判断是否符合条件，才加入到list中
                                                timeList.add(time);
                                                valueList.add(NumberUtil.isNumber(value) ? Convert.toBigDecimal(value) : value);
                                                qList.add(q);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (StrUtil.isNotBlank(code)) {
                            CollUtil.removeNull(qList);
                            Object[][] valueArray = qList.size() == 0 ? new Object[timeList.size()][2] : new Object[timeList.size()][3];
                            for (int j = 0; j < timeList.size(); j++) {
                                valueArray[j][0] = timeList.get(j);
                                valueArray[j][1] = valueList.get(j);
                                if (qList.size() > 0) {
                                    valueArray[j][2] = qList.get(j);
                                }
                            }
                            rsMap.put(code, valueArray);
                        }
                    }
                }
            }
            if (!isBasicResponseExist) {
                throw new BusinessException(result);
            }
        } catch (Exception e) {
            throw new BusinessException("获取时序数据出现异常：{}", e.getMessage());
        } finally {
            IoUtil.close(stringReader);
        }
    }

    @Override
    public void getTimeSeriesStream(OutputStream outputStream, String accessToken, List<Station> stationList, String stime, String etime, String sampled, String resultType, String fillPoint, String filterQ, Map<String, List<String>> moreLimitMap) {
        String avaMethod;
        if ((StrUtil.isBlank(stime) && StrUtil.isBlank(etime)) || (StrUtil.isBlank(stime) && StrUtil.isNotBlank(etime))) {
            // 如果开始和结束时间都为空，或者开始为空，结束不为空，那么将使用getRTDataSnapshot方法
            avaMethod = "getRTDataSnapshot";
        } else {
            avaMethod = "getRTDataHistory";
        }

        long startTime = Convert.toDate(stime) == null ? 0 : Convert.toDate(stime).getTime();
        long endTime = Convert.toDate(etime) == null ? 0 : Convert.toDate(etime).getTime();

        List<String> stationCodeList = stationList.stream().map(Station::getStationNumber).collect(Collectors.toList());

        // 系统接口的输入参数
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("method", avaMethod);
        paramMap.put("codes", CollUtil.join(stationCodeList, ","));

        if (startTime != 0) {
            paramMap.put("startTime", startTime);
        }
        if (endTime != 0) {
            paramMap.put("endTime", endTime);
        }

        if (StrUtil.isNotBlank(filterQ)) {
            paramMap.put("filterQ", filterQ);
            if (NumberUtil.isNumber(filterQ)) {// 对于需要过滤异常测点的情况，需要将测点的上上限和下下限值传入queryExpression参数 (id='xxx' and v>x and v<y) or (id='yyy'....)
                if (MapUtil.isNotEmpty(moreLimitMap)) {
                    List<String> expressionList = new ArrayList<>();
                    stationCodeList.forEach(item -> {
                        List<String> moreLimitList = moreLimitMap.get(item);
                        if (CollUtil.isNotEmpty(moreLimitList) && moreLimitList.size() == 2) {
                            String moreLowerValue = (StrUtil.isBlank(moreLimitList.get(0)) || !NumberUtil.isNumber(moreLimitList.get(0))) ? "" : moreLimitList.get(0);
                            String moreUpperValue = (StrUtil.isBlank(moreLimitList.get(1)) || !NumberUtil.isNumber(moreLimitList.get(1))) ? "" : moreLimitList.get(1);
                            if (StrUtil.isNotBlank(moreLowerValue) && StrUtil.isNotBlank(moreUpperValue)) {
                                expressionList.add(StrUtil.format("(id='{}' and v > {} and v < {})", item, moreLowerValue, moreUpperValue));
                            } else if (StrUtil.isNotBlank(moreLowerValue) && StrUtil.isBlank(moreUpperValue)) {
                                expressionList.add(StrUtil.format("(id='{}' and v > {})", item, moreLowerValue));
                            } else if (StrUtil.isBlank(moreLowerValue) && StrUtil.isNotBlank(moreUpperValue)) {
                                expressionList.add(StrUtil.format("(id='{}' and v < {})", item, moreUpperValue));
                            } else {
                                expressionList.add(StrUtil.format("(id='{}')", item));
                            }
                        }
                    });
                    if (expressionList.size() > 0) {
                        paramMap.put("queryExpression", CollUtil.join(expressionList, " or "));
                    }
                }
            }
        }

        if (StrUtil.equals(avaMethod, "getRTDataHistory")) {
            if (StrUtil.isNotBlank(sampled)) {
                paramMap.put("sampled", sampled);
            }
            if (StrUtil.isNotBlank(resultType)) {
                paramMap.put("resultType", resultType);
            }
            if (StrUtil.isNotBlank(fillPoint)) {
                paramMap.put("fillPoint", fillPoint);
            }
        }

        //首先调用data接口获取测点的统计值
        Map<String, Object[][]> StatResultMap = getTimeSeries(accessToken, CollUtil.join(stationCodeList, ","), "", stime, etime, "", "1", "", "", null, null, "", sampled, "RT_ALL", fillPoint, "", false, filterQ, moreLimitMap);

        InputStream inputStream = null;
        BufferedReader br = null;
        try {
            String queryUrl = StrUtil.format("{}{}", sysConfigService.getAvatarServiceGateway(), AvaConsts.GET_TIME_SERIES_STREAM_URL_INFLUXDB);
            log.info("开始流式查询时序数据");
            inputStream = HttpRequest.post(queryUrl).header("authorization", accessToken).form(paramMap).execute().bodyStream();
            log.info("流式时序数据查询完毕");
            br = new BufferedReader(new InputStreamReader(inputStream));
            String line;
            outputStream.write(StrUtil.bytes("stationNumber,time,value,q,latestValue,mintValue,averageValue,maxValue,siteId,siteName,stationName,kksCode,deviceName,devicePath,bBcsEnName,bcsCHName,abbrClassify,abbrType,required,stationGlobalNumber,physicsDevicePath,stationSource,upperValue,moreUpperValue,lowerValue,moreLowerValue,unit\n"));
            while ((line = br.readLine()) != null) {
                // line逗号分割的第一位是测点原始编码
                List<String> lineList = StrUtil.split(line, ",");
                if (lineList.size() != 4) continue;
                String stationNumber = lineList.get(0);
                String time = DateUtil.format(Convert.toDate(Convert.toLong(lineList.get(1))), "yyyy-MM-dd HH:mm:ss");
                String value = lineList.get(2);
                String q = lineList.get(3);
                Station station = CollUtil.findOneByField(stationList, "stationNumber", stationNumber);
                Object[][] statArray = StatResultMap.get(stationNumber);
                // 最新值
                Object[][] matchArray = ArrayUtil.filter(statArray, item -> CompareUtil.compare(Convert.toInt(item[2]), 0) == 0);
                String latestValue = "";
                if (matchArray.length > 0) {
                    Object[] valueArray = matchArray[0];
                    latestValue = Convert.toStr(valueArray[1]);
                }
                // 最小值
                matchArray = ArrayUtil.filter(statArray, item -> CompareUtil.compare(Convert.toInt(item[2]), 1) == 0);
                String mintValue = "";
                if (matchArray.length > 0) {
                    Object[] valueArray = matchArray[0];
                    mintValue = Convert.toStr(valueArray[1]);
                }
                // 平均值
                matchArray = ArrayUtil.filter(statArray, item -> CompareUtil.compare(Convert.toInt(item[2]), 2) == 0);
                String averageValue = "";
                if (matchArray.length > 0) {
                    Object[] valueArray = matchArray[0];
                    averageValue = Convert.toStr(valueArray[1]);
                }
                // 最大值
                matchArray = ArrayUtil.filter(statArray, item -> CompareUtil.compare(Convert.toInt(item[2]), 3) == 0);
                String maxValue = "";
                if (matchArray.length > 0) {
                    Object[] valueArray = matchArray[0];
                    maxValue = Convert.toStr(valueArray[1]);
                }
                outputStream.write(StrUtil.bytes(StrUtil.format("{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}\n", stationNumber, time, value, q, latestValue, mintValue, averageValue, maxValue, station.getSiteId(), station.getSiteName(), station.getStationName(), station.getKksCode(), station.getDeviceName(), station.getDevicePath(), station.getBcsEnName(), station.getBcsCHName(), station.getAbbrClassify(), station.getAbbrType(), station.getRequired(), station.getStationGlobalNumber(), station.getPhysicsDevicePath(), station.getStationSource(), station.getUpperValue(), station.getMoreUpperValue(), station.getLowerValue(), station.getMoreLowerValue(), station.getUnit())));
            }
        } catch (Exception e) {
            throw new BusinessException("流式查询时序接口出现异常：{}", ExceptionUtil.stacktraceToString(e));
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(br);
        }
    }

    /**
     * @param accessToken     用户token
     * @param stationCodes    测点全局编码，多个逗号分隔
     * @param spsStationCodes 状态信息测点编码
     * @param stime           开始时间
     * @param etime           结束时间
     * @param timeWindows     窗口模式时间参数，按照多个开始时间~结束时间（中间用波浪号分隔），英文逗号分隔的方式
     * @param dataSource      数据源，0为hbase，1为influxdb
     * @param filterExp       过滤符号，支持=，>=，>，<，<=
     * @param filterValue     过滤值
     * @param pageNumber      分页参数
     * @param pageSize        分页参数
     * @param aligned         对齐步长（单位：毫秒）
     * @param sampled         采样步长（单位：秒）
     * @param resultType      采样返回值类型
     * @param fillPoint       补值策略：1: 线性补值, 0: 前值补值, -1：不补值
     * @param siteIds         厂站ID
     * @param isCollaboration 是否协同查询
     * @param filterQ         质量位筛选条件，非数值字符串表示不筛选
     * @param moreLimitMap    测点上上限和下下限map，key为测点全局编码（保持和stationCodes一致），value为list，第一个值为下下限，第二个值为上上限
     * @return 测点的时序数据
     */
    @Override
    public Map<String, Object[][]> getTimeSeries(String accessToken, String stationCodes, String spsStationCodes, String stime, String etime, String timeWindows, String dataSource, String filterExp, String filterValue, Integer pageNumber, Integer pageSize, String aligned, String sampled, String resultType, String fillPoint, String siteIds, Boolean isCollaboration, String filterQ, Map<String, List<String>> moreLimitMap) {
        long one = System.currentTimeMillis();
        if (StrUtil.isBlank(stationCodes)) {
            throw new BusinessException("没有需要查询的测点");
        }
        int maxSeriesSize = sysConfigService.getMaxSeriesSize();
        int maxTsQueryIds = sysConfigService.getAvatarMaxTsQueryIds();
        if (maxSeriesSize > maxTsQueryIds) {
            // 系统的接口单次最多支持maxTsQueryIds个测点的查询
            maxSeriesSize = maxTsQueryIds;
            if (maxSeriesSize == -1) {
                maxSeriesSize = 500;
            }
        }
        String avaMethod;
        if ((StrUtil.isBlank(stime) && StrUtil.isBlank(etime)) || (StrUtil.isBlank(stime) && StrUtil.isNotBlank(etime))) {
            // 如果开始和结束时间都为空，或者开始为空，结束不为空，那么将使用getRTDataSnapshot方法
            avaMethod = "getRTDataSnapshot";
        } else {
            avaMethod = "getRTDataHistory";
            // 查询历史值默认每次查询100个点
            maxSeriesSize = 100;
        }

        // 将时间转换为时间戳
        long startTime = Convert.toDate(stime) == null ? 0 : Convert.toDate(stime).getTime();
        long endTime = Convert.toDate(etime) == null ? 0 : Convert.toDate(etime).getTime();
        List<String> timeWindowsList = StrUtil.split(timeWindows, ",");
        List<String> timeWindowsLongList = new ArrayList<>();
        timeWindowsList.forEach(item -> {
            List<String> itemList = StrUtil.split(item, "~");
            if (itemList.size() == 2) {
                timeWindowsLongList.add(StrUtil.format("{}:{}", Convert.toDate(itemList.get(0)).getTime(), Convert.toDate(itemList.get(1)).getTime()));
            }
        });

        // 由于系统的接口一次传入的测点编码有限制，因此这个对stationCodes需要有个拆分
        List<String> stationCodeList = StrUtil.split(stationCodes, ",", true, true);
        List<String> spsStationCodeList = StrUtil.split(spsStationCodes, ",", true, true);
        stationCodeList = CollUtil.subtractToList(stationCodeList, spsStationCodeList);
        List<List<String>> stationCodeGroups = CollUtil.split(stationCodeList, maxSeriesSize);
        stationCodeGroups.add(spsStationCodeList);
        // 分别对每个分组进行处理
        Map<String, Object[][]> rsMap = new LinkedHashMap<>();
        for (int i = 0; i < stationCodeGroups.size(); i++) {
            long start = System.currentTimeMillis();
            List<String> stationCode = stationCodeGroups.get(i);
            if (stationCode.size() > 0) {
                // 系统接口的输入参数
                HashMap<String, Object> paramMap = new HashMap<>();
                paramMap.put("method", avaMethod);
                paramMap.put("codes", CollUtil.join(stationCode, ","));
                if (startTime != 0) {
                    paramMap.put("startTime", startTime);
                }
                if (endTime != 0) {
                    paramMap.put("endTime", endTime);
                }
                if (StrUtil.isNotBlank(filterQ)) {
                    paramMap.put("filterQ", filterQ);
                    if (NumberUtil.isNumber(filterQ)) {// 对于需要过滤异常测点的情况，需要将测点的上上限和下下限值传入queryExpression参数 (id='xxx' and v>x and v<y) or (id='yyy'....)
                        if (MapUtil.isNotEmpty(moreLimitMap)) {
                            List<String> expressionList = new ArrayList<>();
                            stationCode.forEach(item -> {
                                List<String> moreLimitList = moreLimitMap.get(item);
                                if (CollUtil.isNotEmpty(moreLimitList) && moreLimitList.size() == 2) {
                                    String moreLowerValue = (StrUtil.isBlank(moreLimitList.get(0)) || !NumberUtil.isNumber(moreLimitList.get(0))) ? "" : moreLimitList.get(0);
                                    String moreUpperValue = (StrUtil.isBlank(moreLimitList.get(1)) || !NumberUtil.isNumber(moreLimitList.get(1))) ? "" : moreLimitList.get(1);
                                    if (StrUtil.isNotBlank(moreLowerValue) && StrUtil.isNotBlank(moreUpperValue)) {
                                        expressionList.add(StrUtil.format("(id='{}' and v > {} and v < {})", item, moreLowerValue, moreUpperValue));
                                    } else if (StrUtil.isNotBlank(moreLowerValue) && StrUtil.isBlank(moreUpperValue)) {
                                        expressionList.add(StrUtil.format("(id='{}' and v > {})", item, moreLowerValue));
                                    } else if (StrUtil.isBlank(moreLowerValue) && StrUtil.isNotBlank(moreUpperValue)) {
                                        expressionList.add(StrUtil.format("(id='{}' and v < {})", item, moreUpperValue));
                                    } else {
                                        expressionList.add(StrUtil.format("(id='{}')", item));
                                    }
                                }
                            });
                            if (expressionList.size() > 0) {
                                paramMap.put("queryExpression", CollUtil.join(expressionList, " or "));
                            }
                        }
                    }
                }
                if (StrUtil.equals(avaMethod, "getRTDataHistory") || timeWindowsLongList.size() > 0) {
                    if (StrUtil.isNotBlank(aligned)) {
                        paramMap.put("aligned", aligned);
                    }
                    if (StrUtil.isNotBlank(sampled)) {
                        paramMap.put("sampled", sampled);
                    }
                    if (StrUtil.isNotBlank(resultType)) {
                        paramMap.put("resultType", resultType);
                    }
                    if (StrUtil.isNotBlank(fillPoint)) {
                        paramMap.put("fillPoint", fillPoint);
                    }
                    if (ObjectUtil.isNotEmpty(pageNumber)) {
                        paramMap.put("pageNumber", pageNumber);
                    }
                    if (ObjectUtil.isNotEmpty(pageSize)) {
                        paramMap.put("pageSize", pageSize);
                    }
                    if (i == stationCodeGroups.size() - 1) {
                        // 对于stationCodeGroups的最后一个元素，为状态信息测点，需要设置resultType为RT_SWITCH
                        if (!paramMap.containsKey("resultType")) {
                            paramMap.put("resultType", "RT_SWITCH");
                        }
                    }
                }

                String result = null;
                if (isCollaboration != null && isCollaboration) {
                    result = dataSetTimeSeriesService.getDataSetTimeSeriesData(siteIds, CollUtil.join(stationCode, ","), stime, etime);
                } else if (timeWindowsLongList.size() > 0) {
                    paramMap.put("timeWindows", CollUtil.join(timeWindowsLongList, ","));
                    MapUtil.removeAny(paramMap, "startTime", "endTime", "fillPoint", "method");
                    result = doRequest(HttpConsts.METHOD_POST, AvaConsts.GET_TIME_SERIES_URL_WINDOW_DATA, accessToken, "", paramMap);
                } else if (StrUtil.equals("0", dataSource)) {
                    result = doRequest(HttpConsts.METHOD_POST, AvaConsts.GET_TIME_SERIES_URL_HBASE, accessToken, "", paramMap);
                } else if (StrUtil.equals("1", dataSource)) {
                    result = doRequest(HttpConsts.METHOD_POST, AvaConsts.GET_TIME_SERIES_URL_INFLUXDB, accessToken, "", paramMap);
                } else if (StrUtil.equals("2", dataSource)) {
                    //核心数据
                    paramMap.put("dbName", sysConfigService.getSensitiveStationDBName());
                    result = doRequest(HttpConsts.METHOD_POST, AvaConsts.GET_TIME_SERIES_IDATA_URL_INFLUXDB, accessToken, "", paramMap);
                }
                if (StrUtil.isBlank(result)) {
                    throw new BusinessException("系统的返回结果为空，请检查");
                }

                buildResultMap(rsMap, result, filterExp, filterValue);
            }
            long end = System.currentTimeMillis();
            log.info(StrUtil.format("查询时序库接口第{}次用时：【{}】ms", i+1,end-start));
        }
        long two = System.currentTimeMillis();
        log.info(StrUtil.format("查询时序库接口用时：【{}】ms", two-one));
        return rsMap;
    }



    /**
     * @param accessToken     用户token
     * @param timeWindows     窗口模式时间参数，按照多个开始时间~结束时间（中间用波浪号分隔），英文逗号分隔的方式
     * @param filterExp       过滤符号，支持=，>=，>，<，<=
     * @param filterValue     过滤值
     * @param pageNumber      分页参数
     * @param pageSize        分页参数
     * @param aligned         对齐步长（单位：毫秒）
     * @param sampled         采样步长（单位：秒）
     * @param resultType      采样返回值类型
     * @param fillPoint       补值策略：1: 线性补值, 0: 前值补值, -1：不补值
     * @param siteIds         厂站ID
     * @param filterQ         质量位筛选条件，非数值字符串表示不筛选
     * @param moreLimitMap    测点上上限和下下限map，key为测点全局编码（保持和stationCodes一致），value为list，第一个值为下下限，第二个值为上上限
     * @return 测点的时序数据
     */
    @Override
    public Map<String, Object[][]> getTimeSeriesByCodeWindows(String accessToken,String timeWindows, String filterExp, String filterValue, Integer pageNumber, Integer pageSize, String aligned, String sampled, String resultType, String fillPoint, String siteIds, String filterQ, Map<String, List<String>> moreLimitMap) {
        long one = System.currentTimeMillis();
        if (StrUtil.isBlank(timeWindows)) {
            throw new BusinessException("没有需要查询的测点");
        }
        int maxSeriesSize = sysConfigService.getMaxSeriesSize();
        int maxTsQueryIds = sysConfigService.getAvatarMaxTsQueryIds();
        if (maxSeriesSize > maxTsQueryIds) {
            // 系统的接口单次最多支持maxTsQueryIds个测点的查询
            maxSeriesSize = maxTsQueryIds;
            if (maxSeriesSize == -1) {
                maxSeriesSize = 500;
            }
        }
        String avaMethod= "getRTDataHistory";
        // 查询历史值默认每次查询100个点
        List<String> timeWindowsList = StrUtil.split(timeWindows, ";");
        // 由于系统的接口一次传入的测点编码有限制，因此这个对timeWindowsList需要有个拆分
        List<List<String>> stationCodeGroups = CollUtil.split(timeWindowsList, maxSeriesSize);
        // 分别对每个分组进行处理
        Map<String, Object[][]> rsMap = new LinkedHashMap<>();
        for (int i = 0; i < stationCodeGroups.size(); i++) {
            List<String> stationCode = stationCodeGroups.get(i);
            if (stationCode.size() > 0) {
                // 系统接口的输入参数
                HashMap<String, Object> paramMap = new HashMap<>();
                paramMap.put("method", avaMethod);
                paramMap.put("timeWindows", CollUtil.join(stationCode, ";"));

                if (StrUtil.isNotBlank(filterQ)) {
                    paramMap.put("filterQ", filterQ);
                    if (NumberUtil.isNumber(filterQ)) {// 对于需要过滤异常测点的情况，需要将测点的上上限和下下限值传入queryExpression参数 (id='xxx' and v>x and v<y) or (id='yyy'....)
                        if (MapUtil.isNotEmpty(moreLimitMap)) {
                            List<String> expressionList = new ArrayList<>();
                            stationCode.forEach(item -> {
                                List<String> moreLimitList = moreLimitMap.get(item);
                                if (CollUtil.isNotEmpty(moreLimitList) && moreLimitList.size() == 2) {
                                    String moreLowerValue = (StrUtil.isBlank(moreLimitList.get(0)) || !NumberUtil.isNumber(moreLimitList.get(0))) ? "" : moreLimitList.get(0);
                                    String moreUpperValue = (StrUtil.isBlank(moreLimitList.get(1)) || !NumberUtil.isNumber(moreLimitList.get(1))) ? "" : moreLimitList.get(1);
                                    if (StrUtil.isNotBlank(moreLowerValue) && StrUtil.isNotBlank(moreUpperValue)) {
                                        expressionList.add(StrUtil.format("(id='{}' and v > {} and v < {})", item, moreLowerValue, moreUpperValue));
                                    } else if (StrUtil.isNotBlank(moreLowerValue) && StrUtil.isBlank(moreUpperValue)) {
                                        expressionList.add(StrUtil.format("(id='{}' and v > {})", item, moreLowerValue));
                                    } else if (StrUtil.isBlank(moreLowerValue) && StrUtil.isNotBlank(moreUpperValue)) {
                                        expressionList.add(StrUtil.format("(id='{}' and v < {})", item, moreUpperValue));
                                    } else {
                                        expressionList.add(StrUtil.format("(id='{}')", item));
                                    }
                                }
                            });
                            if (expressionList.size() > 0) {
                                paramMap.put("queryExpression", CollUtil.join(expressionList, " or "));
                            }
                        }
                    }
                }
                if (StrUtil.equals(avaMethod, "getRTDataHistory")) {
                    if (StrUtil.isNotBlank(aligned)) {
                        paramMap.put("aligned", aligned);
                    }
                    if (StrUtil.isNotBlank(sampled)) {
                        paramMap.put("sampled", sampled);
                    }
                    if (StrUtil.isNotBlank(resultType)) {
                        paramMap.put("resultType", resultType);
                    }
                    if (StrUtil.isNotBlank(fillPoint)) {
                        paramMap.put("fillPoint", fillPoint);
                    }
                    if (ObjectUtil.isNotEmpty(pageNumber)) {
                        paramMap.put("pageNumber", pageNumber);
                    }
                    if (ObjectUtil.isNotEmpty(pageSize)) {
                        paramMap.put("pageSize", pageSize);
                    }
                    if (i == stationCodeGroups.size() - 1) {
                        // 对于stationCodeGroups的最后一个元素，为状态信息测点，需要设置resultType为RT_SWITCH
                        if (!paramMap.containsKey("resultType")) {
                            paramMap.put("resultType", "RT_SWITCH");
                        }
                    }
                }

                String result =doRequest(HttpConsts.METHOD_POST, AvaConsts.GET_TIME_SERIES_URL_CODE_WINDOW_DATA, accessToken, "", paramMap);

                if (StrUtil.isBlank(result)) {
                    throw new BusinessException("系统的返回结果为空，请检查");
                }
                buildResultMap(rsMap, result, filterExp, filterValue);
            }
        }
        long two = System.currentTimeMillis();
        log.info(StrUtil.format("查询时序库接口用时：【{}】ms", two-one));
        return rsMap;
    }


    @Override
    public void downloadDataObject(HttpServletResponse response, String accessToken, String dataObjectName, Integer fileType) {
        // 首先调用系统的接口装载数据对象相关的业务数据到文件
        String paramStr = "{\"forceFetch\": 1}";
        String rs = doRequest(HttpConsts.METHOD_POST, StrUtil.format("{}?metaObjectName={}&fileType={}", AvaConsts.LOAD_DATA_TO_FILE_URL, URLUtil.encode(dataObjectName), fileType), accessToken, paramStr, new HashMap<>());
        JSONObject rsObject = JSONUtil.parseObj(rs);
        String statusCode = rsObject.getStr("statusCode");
        String repMessage = rsObject.getStr("repMessage");
        if (!StrUtil.equals(statusCode, "0")) {
            throw new BusinessException(repMessage);
        }
        JSONObject rsDataObject = JSONUtil.parseObj(rsObject.get("data"));
        // 获取到数据文件的路径
        String filePath = rsDataObject.getStr("filePath");
        //获取系统URL地址
        String url = sysConfigService.getAvatarServiceGateway();
        boolean fileReady = false;
        int retryCount = 0;
        while (!fileReady) {
            retryCount++;
            if (retryCount > 10) {
                throw new BusinessException("文件生成超时");
            }
            log.info("开始判断文件{}是否生成", filePath);
            String result = HttpRequest.post(StrUtil.format("{}{}", url, AvaConsts.QUERY_FILE_READY_URL)).header("authorization", accessToken).header("Content-Type", "text/plain;charset=UTF-8").body(filePath).execute().body();
            log.info("系统返回的数据：{}", result);
            JSONObject object = JSONUtil.parseObj(result);
            String code = object.getStr("statusCode");
            String message = object.getStr("repMessage");
            if (!StrUtil.equals(code, "0")) {
                throw new BusinessException(message);
            }
            fileReady = object.getBool("data");
            if (!fileReady) {
                log.info("文件还未生成，等待2S后重试");
                ThreadUtil.safeSleep(2000);
            }
        }

        log.info("文件{}已经生成，开始下载文件", filePath);
        byte[] rsBytes = HttpRequest.post(StrUtil.format("{}{}", url, AvaConsts.GET_FILE_URL)).header("authorization", accessToken).header("Content-Type", "text/plain;charset=UTF-8").body(filePath).execute().bodyBytes();
        try {
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(rsBytes);
        } catch (IOException e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public void logDownload(HttpServletResponse response) throws BusinessException {
        String folderName = System.getProperty("catalina.home") + File.separator + "logs" + File.separator + DateUtil.format(DateUtil.date(), "yyyy-MM-dd");
        String tempPath = System.getProperty("java.io.tmpdir") + File.separator + UUIDUtil.generateUUID() + ".xlsx";
        ZipUtil.zip(folderName, tempPath);
        File file = new File(tempPath);

        if (!file.exists()) {
            throw new BusinessException(tempPath + "文件不存在");
        }
        response.setContentType("application/force-download");
        response.addHeader("Content-Disposition", "attachment;fileName=" + tempPath);
        try {
            byte[] buffer = new byte[1024];
            try (FileInputStream fis = new FileInputStream(file); BufferedInputStream bis = new BufferedInputStream(fis)) {

                OutputStream os = response.getOutputStream();

                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer, 0, i);
                    i = bis.read(buffer);
                }
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }


    }

    /**
     * 判断value值是否满足filter条件
     */
    @Override
    public boolean validate(String value, String filterExp, String filterValue) {
        if (StrUtil.isBlank(filterExp)) {
            return true;
        }
        if (StrUtil.isNotBlank(filterExp) && StrUtil.isBlank(filterValue)) {
            throw new BusinessException("参数传入错误，filterExp有值，filterValue必须有值");
        }
        if (!NumberUtil.isNumber(value)) {
            //如果value不是数值，而又需要进行过滤，那么直接返回false，不显示对应的数据
            return false;
        }
        int compareValue = NumberUtil.compare(Convert.toDouble(value), Convert.toDouble(filterValue));
        switch (filterExp) {
            case ">=": {
                return compareValue == 0 || compareValue == 1;
            }
            case ">": {
                return compareValue == 1;
            }
            case "<=": {
                return compareValue == 0 || compareValue == -1;
            }
            case "<": {
                return compareValue == -1;
            }
            case "=": {
                return compareValue == 0;
            }
            default: {
                throw new BusinessException("参数传入错误，filterExp只支持=，>=，>，<，<=");
            }
        }
    }

    @Override
    public void downloadStandardDetail(HttpServletResponse response, String accessToken, String standardName, String type) {
        //获取系统URL地址
        String serviceGateway = sysConfigService.getAvatarServiceGateway();
        String url = StrUtil.format("{}{}?standardName={}&type={}", serviceGateway, AvaConsts.DEVICE_OBJECT_DATA_DOWNLOAD, standardName, type);
        byte[] bodyBytes = HttpRequest.get(url).header("authorization", accessToken).header("Content-Type", "text/plain;charset=UTF-8").execute().bodyBytes();
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(bodyBytes);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 对调用结果进行处理，如果出现调用失败，有可能是access_token过期，需要获取新的token重新调用
     */
    private String handleResult(String statusCode, String queryUrl, String paramStr, Map<String, Object> paramMap) {
        String accessToken;
        String result = "";
        String repMessage = null;
        //设置计数器，只重新获取三次，若超过三次，则返回失败信息
        int count = 1;
        while (!"0".equals(statusCode)) {
            //首先删除redis中的access_token
            dataCacheService.removeObject("avatar_scheduler_user");
            Map<String, Object> defaultUserMap = sysConfigService.getAvatarDefaultUserInfo();
            accessToken = getAvatarToken(EncryptUtil.rsaDecryptOrNothing(Convert.toStr(defaultUserMap.get("userName"))), EncryptUtil.rsaDecryptOrNothing(Convert.toStr(defaultUserMap.get("password"))));
            if (!StrUtil.isBlank(paramStr) && paramMap.size() == 0) {
                result = HttpRequest.post(queryUrl).header("authorization", accessToken).body(paramStr).execute().body();
            } else if (StrUtil.isBlank(paramStr) && paramMap.size() > 0) {
                result = HttpRequest.post(queryUrl).header("authorization", accessToken).form(paramMap).execute().body();
            } else if (!StrUtil.isBlank(paramStr) && paramMap.size() > 0) {
                result = HttpRequest.post(queryUrl).header("authorization", accessToken).form(paramMap).body(paramStr).execute().body();
            } else {
                result = HttpRequest.post(queryUrl).header("authorization", accessToken).execute().body();
            }

            StringReader reader = null;
            try {
                reader = new StringReader(result);
                JsonFactory jsonFactory = new MappingJsonFactory();
                JsonParser jsonParser = jsonFactory.createParser(reader);
                while (!jsonParser.isClosed()) {
                    JsonToken jsonToken = jsonParser.nextToken();
                    if (JsonToken.FIELD_NAME.equals(jsonToken)) {
                        String fieldName = jsonParser.getCurrentName();
                        // 这里只获取statusCode的值，如果statusCode为-88888，需要特殊处理
                        if (StrUtil.equals(fieldName, "statusCode")) {
                            jsonParser.nextToken();
                            statusCode = jsonParser.getValueAsString();
                        }
                    }
                }
            } catch (Exception e) {
                throw new BusinessException(StrUtil.format("返回的结果{}.......无法解析", StrUtil.sub(result, 0, 100)));
            } finally {
                IoUtil.close(reader);
            }
            count++;
            if (count > 3) {
                throw new BusinessException("重新获取系统token调用服务失败：{}，请检查。", repMessage);
            }
        }

        return result;
    }

    private void showRequestInfo(String queryUrl, String accessToken, String paramStr, Map<String, Object> paramMap) {
        log.debug("===========================调用信息==========================================");
        log.debug("url: " + queryUrl);
        log.debug("accessToken: " + accessToken);
        log.debug("paramStr: " + paramStr);
        log.debug("paramMap: " + paramMap);
        log.debug("============================================================================");
    }

    private void showResponseInfo(String result) {
        log.debug("==========================返回信息===========================================");
        log.debug("result: " + result);
        log.debug("============================================================================");
    }
}
