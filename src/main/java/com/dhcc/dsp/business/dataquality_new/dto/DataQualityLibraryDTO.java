package com.dhcc.dsp.business.dataquality_new.dto;

import com.dhcc.dsp.business.dataquality_new.model.DataQualityLibraryParam;
import lombok.Data;

import java.util.List;

@Data
public class DataQualityLibraryDTO {

    private String id;
    private String pId;
    private Integer type;
    private String policyName;
    private List<String> siteIdList;
    private String businessDefine;
    private String judgeDefine;
    private String otherDesc;
    private String sample;
    private String samplePic;
    private Integer auditProType;
    private String auditProId;
    private String auditProName;
    private String exceptionName;
    private Integer auditObjectType;
    private String auditObject;
    private boolean enableStationCombination;
    List<LogicFeatureDTO> featureList;
    List<DataQualityLibraryParam> paramList;

    private String nodeRedId;
    private String spaceId;

}
