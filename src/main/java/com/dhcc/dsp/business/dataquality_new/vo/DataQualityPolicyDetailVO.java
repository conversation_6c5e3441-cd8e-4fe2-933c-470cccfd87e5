package com.dhcc.dsp.business.dataquality_new.vo;

import com.dhcc.dsp.business.dataquality_new.model.DataQualityLibraryParam;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DataQualityPolicyDetailVO {

    private String id;

    private String libraryId;

    private String policeName;

    private Integer auditProType;

    private String auditProId;

    private String auditProName;

    private Integer auditObjectType;

    private String auditObject;

    private String exceptionName;

    private String businessDefine;

    private String judgeDefine;

    private String otherDesc;

    private String sample;

    private String samplePic;

    private boolean enableStationCombination;

    private String createTime;

    private String createUser;

    private String updateTime;

    private String userName;

    private List<String> siteIdList;

    private List<DataQualityLibraryParam> paramList;

    private List<Map<String, Object>> featureList;

    private String nodeRedId;

    private String spaceId;
}
