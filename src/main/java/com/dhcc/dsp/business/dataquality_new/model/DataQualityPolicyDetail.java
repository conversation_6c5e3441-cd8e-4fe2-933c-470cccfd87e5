package com.dhcc.dsp.business.dataquality_new.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("dq_new_policy_detail")
public class DataQualityPolicyDetail implements Serializable {

    private static final long serialVersionUID = 3123667773323243608L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("library_id")
    private String libraryId;

    @TableField("police_name")
    private String policeName;

    @TableField("audit_pro_type")
    private Integer auditProType;

    @TableField("audit_pro_id")
    private String auditProId;

    @TableField("audit_pro_name")
    private String auditProName;

    @TableField("audit_object_type")
    private Integer auditObjectType;

    @TableField("audit_object")
    private String auditObject;

    @TableField("exception_name")
    private String exceptionName;

    @TableField("business_define")
    private String businessDefine;

    @TableField("judge_define")
    private String judgeDefine;

    @TableField("other_desc")
    private String otherDesc;

    @TableField("sample")
    private String sample;

    @TableField("sample_pic")
    private String samplePic;

    @TableField("enable_station_combination")
    private boolean enableStationCombination;

    @TableField("create_time")
    private String createTime;

    @TableField("create_user")
    private String createUser;

    @TableField("update_time")
    private String updateTime;

    @TableField(exist = false)
    private String userName;

    @TableField("node_red_id")
    private String nodeRedId;

    @TableField("space_id")
    private String spaceId;

}
