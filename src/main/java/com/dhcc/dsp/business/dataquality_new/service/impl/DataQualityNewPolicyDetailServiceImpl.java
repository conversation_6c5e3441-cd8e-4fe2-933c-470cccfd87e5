package com.dhcc.dsp.business.dataquality_new.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dhcc.dsp.business.dataquality_new.dao.DataQualityNewPolicyDetailMapper;
import com.dhcc.dsp.business.dataquality_new.model.DataQualityLibraryFeature;
import com.dhcc.dsp.business.dataquality_new.model.DataQualityLibraryParam;
import com.dhcc.dsp.business.dataquality_new.model.DataQualityLibrarySite;
import com.dhcc.dsp.business.dataquality_new.model.DataQualityPolicyDetail;
import com.dhcc.dsp.business.dataquality_new.service.DataQualityNewLibraryFeatureService;
import com.dhcc.dsp.business.dataquality_new.service.DataQualityNewLibraryParamService;
import com.dhcc.dsp.business.dataquality_new.service.DataQualityNewLibrarySiteService;
import com.dhcc.dsp.business.dataquality_new.service.DataQualityNewPolicyDetailService;
import com.dhcc.dsp.business.dataquality_new.model.DataQualityTask;
import com.dhcc.dsp.business.dataquality_new.service.DataQualityNewTaskService;
import com.dhcc.dsp.business.dataquality_new.vo.DataQualityPolicyDetailVO;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DataQualityNewPolicyDetailServiceImpl extends ServiceImpl<DataQualityNewPolicyDetailMapper, DataQualityPolicyDetail> implements DataQualityNewPolicyDetailService {

    private final DataQualityNewLibrarySiteService dataQualityLibrarySiteService;
    private final DataQualityNewLibraryParamService dataQualityLibraryParamService;
    private final DataQualityNewLibraryFeatureService dataQualityLibraryFeatureService;
    private final DataQualityNewTaskService dataQualityNewTaskService;

    public DataQualityNewPolicyDetailServiceImpl(DataQualityNewLibrarySiteService dataQualityNewLibrarySiteService, DataQualityNewLibraryParamService dataQualityNewLibraryParamService, DataQualityNewLibraryFeatureService dataQualityNewLibraryFeatureService, DataQualityNewTaskService dataQualityNewTaskService) {
        this.dataQualityLibrarySiteService = dataQualityNewLibrarySiteService;
        this.dataQualityLibraryParamService = dataQualityNewLibraryParamService;
        this.dataQualityLibraryFeatureService = dataQualityNewLibraryFeatureService;
        this.dataQualityNewTaskService = dataQualityNewTaskService;
    }

    @Override
    public DataQualityPolicyDetailVO getDetailByLibraryId(String libraryId) {
        DataQualityPolicyDetail detail = this.getOne(new QueryWrapper<DataQualityPolicyDetail>().select("*", "(select username from ddaas_users t1 where t1.userid = dq_new_policy_detail.create_user) as userName").eq("library_id", libraryId));
        DataQualityPolicyDetailVO detailVO = new DataQualityPolicyDetailVO();
        BeanUtil.copyProperties(detail, detailVO);
        List<DataQualityLibrarySite> sites = dataQualityLibrarySiteService.list(new QueryWrapper<DataQualityLibrarySite>().eq("library_id", libraryId));
        if (CollUtil.isNotEmpty(sites)) {
            detailVO.setSiteIdList(sites.stream().map(DataQualityLibrarySite::getSiteId).collect(Collectors.toList()));
        }
        detailVO.setParamList(dataQualityLibraryParamService.list(new QueryWrapper<DataQualityLibraryParam>().eq("library_id", libraryId)));
        List<DataQualityLibraryFeature> features = dataQualityLibraryFeatureService.list(new QueryWrapper<DataQualityLibraryFeature>().eq("library_id", libraryId));
        if (CollUtil.isNotEmpty(features)) {
            detailVO.setFeatureList(baseMapper.getFeatureList(features.stream().map(f -> StrUtil.format("'{}'", f.getLibraryId())).collect(Collectors.joining(","))));
        }

        // 查询相关任务的 flow_task_id 和 flow_serve_id
        DataQualityTask task = dataQualityNewTaskService.getOne(new QueryWrapper<DataQualityTask>().eq("library_id", libraryId).last("LIMIT 1"));
        if (task != null) {
            detailVO.setNodeRedId(task.getFlowTaskId());
            detailVO.setSpaceId(task.getFlowServeId());
        }

        return detailVO;
    }
}
