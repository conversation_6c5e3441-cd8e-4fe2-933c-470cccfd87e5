package com.dhcc.dsp.business.dataquality_new.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dhcc.avatar.domain.AvatarThreadContext;
import com.dhcc.dsp.business.dataquality_new.dao.DataQualityNewLibraryMapper;
import com.dhcc.dsp.business.dataquality_new.dto.DataQualityLibraryDTO;
import com.dhcc.dsp.business.dataquality_new.dto.LogicFeatureDTO;
import com.dhcc.dsp.business.dataquality_new.model.*;
import com.dhcc.dsp.business.dataquality_new.service.*;
import com.dhcc.dsp.business.dataquality_new.vo.DataQualityPolicyDetailVO;
import com.dhcc.dsp.common.exception.BusinessException;
import com.dhcc.dsp.common.utils.TreeNodeUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DataQualityNewLibraryServiceImpl extends ServiceImpl<DataQualityNewLibraryMapper, DataQualityPolicyLibrary> implements DataQualityNewLibraryService {
    private final DataQualityNewPolicyDetailService dataQualityPolicyDetailService;
    private final DataQualityNewLibrarySiteService dataQualityLibrarySiteService;
    private final DataQualityNewTaskService dataQualityTaskService;
    private final DataQualityNewLibraryFeatureService dataQualityLibraryFeatureService;
    private final DataQualityNewLibraryParamService dataQualityLibraryParamService;
    private final DataQualityNewParamRelateService dataQualityParamRelateService;

    public DataQualityNewLibraryServiceImpl(DataQualityNewPolicyDetailService dataQualityNewPolicyDetailService, DataQualityNewLibrarySiteService dataQualityNewLibrarySiteService, DataQualityNewTaskService dataQualityNewTaskService, DataQualityNewLibraryFeatureService dataQualityNewLibraryFeatureService, DataQualityNewLibraryParamService dataQualityNewLibraryParamService, DataQualityNewParamRelateService dataQualityNewParamRelateService) {
        this.dataQualityPolicyDetailService = dataQualityNewPolicyDetailService;
        this.dataQualityLibrarySiteService = dataQualityNewLibrarySiteService;
        this.dataQualityTaskService = dataQualityNewTaskService;
        this.dataQualityLibraryFeatureService = dataQualityNewLibraryFeatureService;
        this.dataQualityLibraryParamService = dataQualityNewLibraryParamService;
        this.dataQualityParamRelateService = dataQualityNewParamRelateService;
    }

    @Override
    public List<DataQualityPolicyLibrary> getLibraryTree(String id, Integer auditObjectType) {
        QueryWrapper<DataQualityPolicyLibrary> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(id)) {
            DataQualityPolicyLibrary library = this.getById(id);
            queryWrapper.likeRight("path", StrUtil.format("{}~", library.getPath())).or().eq("path", library.getPath());
        }
        if (auditObjectType != null) {
            queryWrapper.exists("select 1 from dq_new_policy_detail t1 where dq_new_policy_library.id = t1.library_id and t1.audit_object_type = " + auditObjectType).or().ne("type", 2);
        }
        queryWrapper.select("*,(select audit_pro_type  from dq_new_policy_detail t1 where dq_new_policy_library.id = t1.library_id) as auditProType");
        return TreeNodeUtil.assembleTree(this.list(queryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLibrary(DataQualityLibraryDTO dataQualityLibraryDTO) {
        String now = DateUtil.now();
        if (StrUtil.isBlank(dataQualityLibraryDTO.getId())) {
            //新增
//            if (dataQualityPolicyDetailService.count(new QueryWrapper<DataQualityPolicyDetail>().eq("audit_pro_id", dataQualityLibraryDTO.getAuditProId())) > 0) {
//                throw new BusinessException("该流程或程序已经被绑定，不允许重复绑定");
//            }
            DataQualityPolicyLibrary library = new DataQualityPolicyLibrary();
            library.setPid(StrUtil.isBlank(dataQualityLibraryDTO.getPId()) ? "0" : dataQualityLibraryDTO.getPId());
            library.setType(dataQualityLibraryDTO.getType());
            if ("0".equals(library.getPid())) {
                library.setPath(dataQualityLibraryDTO.getPolicyName());
            } else {
                DataQualityPolicyLibrary parent = this.getById(library.getPid());
                if (parent == null) {
                    throw new BusinessException("pid【{}】数据不存在", library.getPid());
                }
                library.setPath(StrUtil.format("{}~{}", parent.getPath(), dataQualityLibraryDTO.getPolicyName()));
            }
            library.setName(dataQualityLibraryDTO.getPolicyName());
            if (this.count(new QueryWrapper<DataQualityPolicyLibrary>().eq("path", library.getPath())) > 0) {
                throw new BusinessException("【{}】已经存在", library.getPath());
            }
            this.save(library);
            if (dataQualityLibraryDTO.getType() == 2) {
                //保存明细
                if (CollUtil.isEmpty(dataQualityLibraryDTO.getSiteIdList())) {
                    throw new BusinessException("厂站不能为空");
                }

                DataQualityPolicyDetail detail = new DataQualityPolicyDetail();
                detail.setLibraryId(library.getId());
                detail.setPoliceName(library.getName());
                detail.setAuditProType(dataQualityLibraryDTO.getAuditProType());
                detail.setAuditProId(dataQualityLibraryDTO.getAuditProId());
                detail.setAuditProName(dataQualityLibraryDTO.getAuditProName());
                detail.setBusinessDefine(dataQualityLibraryDTO.getBusinessDefine());
                detail.setJudgeDefine(dataQualityLibraryDTO.getJudgeDefine());
                detail.setOtherDesc(dataQualityLibraryDTO.getOtherDesc());
                detail.setSample(dataQualityLibraryDTO.getSample());
                detail.setSamplePic(dataQualityLibraryDTO.getSamplePic());
                detail.setAuditObjectType(dataQualityLibraryDTO.getAuditObjectType());
                detail.setAuditObject(dataQualityLibraryDTO.getAuditObject());
                detail.setExceptionName(dataQualityLibraryDTO.getExceptionName());
                detail.setEnableStationCombination(dataQualityLibraryDTO.isEnableStationCombination());
                detail.setNodeRedId(dataQualityLibraryDTO.getNodeRedId());
                detail.setSpaceId(dataQualityLibraryDTO.getSpaceId());
                detail.setCreateTime(now);
                detail.setCreateUser(AvatarThreadContext.userId.get());
                detail.setUpdateTime(now);
                dataQualityPolicyDetailService.save(detail);
                //保存厂站关联关系
                List<DataQualityLibrarySite> dataQualityLibrarySites = new ArrayList<>();
                for (String s : dataQualityLibraryDTO.getSiteIdList()) {
                    DataQualityLibrarySite site = new DataQualityLibrarySite();
                    site.setLibraryId(library.getId());
                    site.setSiteId(s);
                    dataQualityLibrarySites.add(site);
                }
                dataQualityLibrarySiteService.saveBatch(dataQualityLibrarySites);
                //保存关联的逻辑测点和参数
                if (CollUtil.isNotEmpty(dataQualityLibraryDTO.getFeatureList())) {
                    List<DataQualityLibraryFeature> dataQualityLibraryFeatures = new ArrayList<>();
                    for (LogicFeatureDTO logicFeature : dataQualityLibraryDTO.getFeatureList()) {
                        DataQualityLibraryFeature feature = new DataQualityLibraryFeature();
                        feature.setLibraryId(library.getId());
                        feature.setEnName(logicFeature.getBcsEnName());
                        feature.setLogicPath(logicFeature.getDevicePath());
                        dataQualityLibraryFeatures.add(feature);
                    }
                    dataQualityLibraryFeatureService.saveBatch(dataQualityLibraryFeatures);
                }
                if (CollUtil.isNotEmpty(dataQualityLibraryDTO.getParamList())) {
                    for (DataQualityLibraryParam dataQualityLibraryParam : dataQualityLibraryDTO.getParamList()) {
                        dataQualityLibraryParam.setLibraryId(library.getId());
                    }
                    dataQualityLibraryParamService.saveBatch(dataQualityLibraryDTO.getParamList());
                }
            }
        } else {
            //编辑
            DataQualityPolicyLibrary recentLibrary = this.getById(dataQualityLibraryDTO.getId());
            DataQualityPolicyDetailVO recentDetail = dataQualityPolicyDetailService.getDetailByLibraryId(dataQualityLibraryDTO.getId());
            if (recentDetail == null) {
                throw new BusinessException("【{}】数据不存在", dataQualityLibraryDTO.getId());
            }
            DataQualityPolicyLibrary library = new DataQualityPolicyLibrary();
            library.setId(dataQualityLibraryDTO.getId());
            library.setPid(StrUtil.isBlank(dataQualityLibraryDTO.getPId()) ? "0" : dataQualityLibraryDTO.getPId());
            library.setType(dataQualityLibraryDTO.getType());
            if ("0".equals(library.getPid())) {
                library.setPath(dataQualityLibraryDTO.getPolicyName());
            } else {
                DataQualityPolicyLibrary parent = this.getById(library.getPid());
                if (parent == null) {
                    throw new BusinessException("pid【{}】数据不存在", library.getPid());
                }
                library.setPath(StrUtil.format("{}~{}", parent.getPath(), dataQualityLibraryDTO.getPolicyName()));
            }
            library.setName(dataQualityLibraryDTO.getPolicyName());
            if (this.count(new QueryWrapper<DataQualityPolicyLibrary>().eq("path", library.getPath()).ne("id", dataQualityLibraryDTO.getId())) > 0) {
                throw new BusinessException("【{}】已经存在", library.getPath());
            }
            this.updateById(library);
            if (dataQualityLibraryDTO.getType() == 1 && !StrUtil.equals(recentDetail.getPoliceName(), dataQualityLibraryDTO.getPolicyName())) {
                //如果修改了二级的名称，需要修改下级的目录
                List<DataQualityPolicyLibrary> children = this.list(new QueryWrapper<DataQualityPolicyLibrary>().likeRight("path", StrUtil.format("{}~", recentLibrary.getPath())));
                for (DataQualityPolicyLibrary child : children) {
                    child.setPath(child.getPath().replaceFirst(recentLibrary.getPath(), library.getPath()));
                }
                this.updateBatchById(children);
            }
            if (dataQualityLibraryDTO.getType() == 2) {
                //保存明细
                if (CollUtil.isEmpty(dataQualityLibraryDTO.getSiteIdList())) {
                    throw new BusinessException("厂站不能为空");
                }
                DataQualityPolicyDetail detail = new DataQualityPolicyDetail();
                detail.setId(recentDetail.getId());
                detail.setLibraryId(library.getId());
                detail.setPoliceName(library.getName());
                detail.setAuditProType(dataQualityLibraryDTO.getAuditProType());
                detail.setAuditProId(dataQualityLibraryDTO.getAuditProId());
                detail.setAuditProName(dataQualityLibraryDTO.getAuditProName());
                detail.setBusinessDefine(dataQualityLibraryDTO.getBusinessDefine());
                detail.setJudgeDefine(dataQualityLibraryDTO.getJudgeDefine());
                detail.setOtherDesc(dataQualityLibraryDTO.getOtherDesc());
                detail.setSample(dataQualityLibraryDTO.getSample());
                detail.setSamplePic(dataQualityLibraryDTO.getSamplePic());
                detail.setNodeRedId(dataQualityLibraryDTO.getNodeRedId());
                detail.setSpaceId(dataQualityLibraryDTO.getSpaceId());
                List<DataQualityLibrarySite> recentLibrarySites = dataQualityLibrarySiteService.list(new QueryWrapper<DataQualityLibrarySite>().eq("library_id", dataQualityLibraryDTO.getId()));
                if (dataQualityTaskService.count(new QueryWrapper<DataQualityTask>().eq("library_id", dataQualityLibraryDTO.getId())) > 0) {
                    //有关联的任务,只允许编辑【策略名称】【业务定义】【判断逻辑】【其他描述信息】【样例】【样例图片】这类描述信息，不可以更新策略程序配置信息，并且【应用范围】只可以增加或取消尚未建立任务的厂站
                    List<String> removeSiteIds = recentLibrarySites.stream().filter(s -> !dataQualityLibraryDTO.getSiteIdList().contains(s.getSiteId())).map(DataQualityLibrarySite::getSiteId).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(removeSiteIds)) {
                        if (dataQualityTaskService.count(new QueryWrapper<DataQualityTask>().eq("library_id", dataQualityLibraryDTO.getId()).in("site_id", removeSiteIds)) > 0) {
                            throw new BusinessException("已创建关联任务的场站不能删除");
                        }
                    }
                    detail.setUpdateTime(now);
                } else {
                    //没有关联任务，可以更新其他信息
                    detail.setAuditObjectType(dataQualityLibraryDTO.getAuditObjectType());
                    detail.setAuditObject(dataQualityLibraryDTO.getAuditObject());
                    detail.setExceptionName(dataQualityLibraryDTO.getExceptionName());
                    detail.setEnableStationCombination(dataQualityLibraryDTO.isEnableStationCombination());
                    detail.setNodeRedId(dataQualityLibraryDTO.getNodeRedId());
                    detail.setSpaceId(dataQualityLibraryDTO.getSpaceId());
                    detail.setUpdateTime(now);
                    //保存关联的逻辑测点和参数
                    dataQualityLibraryFeatureService.remove(new QueryWrapper<DataQualityLibraryFeature>().eq("library_id", library.getId()));
                    if (CollUtil.isNotEmpty(dataQualityLibraryDTO.getFeatureList())) {
                        List<DataQualityLibraryFeature> dataQualityLibraryFeatures = new ArrayList<>();
                        for (LogicFeatureDTO logicFeature : dataQualityLibraryDTO.getFeatureList()) {
                            DataQualityLibraryFeature feature = new DataQualityLibraryFeature();
                            feature.setLibraryId(library.getId());
                            feature.setEnName(logicFeature.getBcsEnName());
                            feature.setLogicPath(logicFeature.getDevicePath());
                            dataQualityLibraryFeatures.add(feature);
                        }
                        dataQualityLibraryFeatureService.saveBatch(dataQualityLibraryFeatures);
                    }
                    if (CollUtil.isNotEmpty(dataQualityLibraryDTO.getParamList())) {
                        for (DataQualityLibraryParam dataQualityLibraryParam : dataQualityLibraryDTO.getParamList()) {
                            dataQualityLibraryParam.setLibraryId(library.getId());
                        }
                        dataQualityLibraryParamService.updateBatchById(dataQualityLibraryDTO.getParamList());
                    }
                }
                dataQualityPolicyDetailService.updateById(detail);
                //保存厂站关联关系
                List<DataQualityLibrarySite> dataQualityLibrarySites = new ArrayList<>();
                for (String s : dataQualityLibraryDTO.getSiteIdList()) {
                    DataQualityLibrarySite site = new DataQualityLibrarySite();
                    site.setLibraryId(library.getId());
                    site.setSiteId(s);
                    dataQualityLibrarySites.add(site);
                }
                dataQualityLibrarySiteService.remove(new QueryWrapper<DataQualityLibrarySite>().eq("library_id", dataQualityLibraryDTO.getId()));
                dataQualityLibrarySiteService.saveBatch(dataQualityLibrarySites);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeLibrary(String id) {
        DataQualityPolicyLibrary library = this.getById(id);
        if (library != null) {
            if (library.getType() == 0) {
                throw new BusinessException("不能删除根节点数据");
            }
            if (library.getType() == 1) {
                List<DataQualityPolicyLibrary> children = this.list(new QueryWrapper<DataQualityPolicyLibrary>().likeRight("path", StrUtil.format("{}~", library.getPath())).or().eq("path", library.getPath()));
                List<String> childrenIdList = children.stream().map(DataQualityPolicyLibrary::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(children)) {
                    if (dataQualityTaskService.count(new QueryWrapper<DataQualityTask>().in("library_id", childrenIdList)) > 0) {
                        throw new BusinessException("该库下存在任务，不能删除");
                    }
                    this.removeBatchByIds(childrenIdList);
                    dataQualityPolicyDetailService.remove(new QueryWrapper<DataQualityPolicyDetail>().in("library_id", childrenIdList));
                    dataQualityLibrarySiteService.remove(new QueryWrapper<DataQualityLibrarySite>().in("library_id", childrenIdList));
                    dataQualityLibraryFeatureService.remove(new QueryWrapper<DataQualityLibraryFeature>().in("library_id", childrenIdList));
                    dataQualityLibraryParamService.remove(new QueryWrapper<DataQualityLibraryParam>().in("library_id", childrenIdList));
                    dataQualityParamRelateService.remove(new QueryWrapper<DataQualityParamRelate>().eq("library_id", library.getId()));
                }
            } else if (library.getType() == 2) {
                if (dataQualityTaskService.count(new QueryWrapper<DataQualityTask>().eq("library_id", library.getId())) > 0) {
                    throw new BusinessException("该库下存在任务，不能删除");
                }
                this.removeById(id);
                dataQualityPolicyDetailService.remove(new QueryWrapper<DataQualityPolicyDetail>().eq("library_id", id));
                dataQualityLibrarySiteService.remove(new QueryWrapper<DataQualityLibrarySite>().eq("library_id", id));
                dataQualityLibraryFeatureService.remove(new QueryWrapper<DataQualityLibraryFeature>().eq("library_id", id));
                dataQualityLibraryParamService.remove(new QueryWrapper<DataQualityLibraryParam>().eq("library_id", id));
                dataQualityParamRelateService.remove(new QueryWrapper<DataQualityParamRelate>().eq("library_id", library.getId()));
            } else {
                throw new BusinessException("不允许删除根节点");
            }
        }
    }

    @Override
    public Map<String, List<Map<String, Object>>> getLibraryGroup() {
        List<Map<String, Object>> list = baseMapper.getLibraryGroup();
        Map<String, List<Map<String, Object>>> auditObjectType = list.stream().collect(Collectors.groupingBy(map -> map.get("auditObjectType").toString()));
        return auditObjectType;
    }

    @Override
    public List<DataQualityPolicyLibrary> getTimeSeriesLibrary() {
        return this.list(new QueryWrapper<DataQualityPolicyLibrary>().likeRight("path", "时序数据~").eq("type", 2));
    }
}
