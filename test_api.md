# 数据质量策略保存和查询接口修改

## 修改内容

已成功为数据质量策略添加了两个新字段的保存和查询功能：

1. **nodeRedId** - 节点红色ID字段
2. **spaceId** - 空间ID字段

## 修改的文件

### 1. DataQualityLibraryDTO.java
在保存策略的DTO中添加了两个新字段：
```java
private String nodeRedId;
private String spaceId;
```

### 2. DataQualityPolicyDetail.java
在策略详情实体中添加了两个新字段：
```java
@TableField("node_red_id")
private String nodeRedId;

@TableField("space_id")
private String spaceId;
```

### 3. DataQualityPolicyDetailVO.java
在返回的VO中添加了两个新字段：
```java
private String nodeRedId;
private String spaceId;
```

### 4. DataQualityNewLibraryServiceImpl.java
在 `saveLibrary` 方法中添加了保存这两个字段的逻辑：
- 新增策略时保存这两个字段
- 编辑策略时更新这两个字段（无论是否有关联任务）

## 接口测试

### 1. 保存策略接口
```
POST /dataService_quality/service/dataQuality-new/saveLibrary
```

请求体示例：
```json
{
  "policyName": "测试策略",
  "type": 2,
  "auditObjectType": 1,
  "auditObject": "测试对象",
  "exceptionName": "测试异常",
  "nodeRedId": "node_red_123",
  "spaceId": "space_456",
  "siteIdList": ["site1", "site2"],
  // ... 其他字段
}
```

### 2. 查询策略详情接口
```
POST /dataService_quality/service/dataQuality-new/getPolicyDetail?libraryId=21d790813cff2d07b815f0cafd907efc
```

预期响应：
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": "...",
    "libraryId": "21d790813cff2d07b815f0cafd907efc",
    "policeName": "...",
    "nodeRedId": "node_red_123",
    "spaceId": "space_456",
    // ... 其他现有字段
  }
}
```

## 注意事项

1. **数据库字段映射**：`nodeRedId` 对应数据库字段 `node_red_id`，`spaceId` 对应 `space_id`
2. **向后兼容性**：修改完全向后兼容，不会影响现有功能
3. **保存和查询一致性**：保存时传入的 `nodeRedId` 和 `spaceId` 值会在查询时原样返回
