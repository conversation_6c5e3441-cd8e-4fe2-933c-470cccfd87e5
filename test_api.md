# 数据质量策略详情接口测试

## 修改内容

已成功为 `getPolicyDetail` 接口添加了两个新字段：

1. **nodeRedId** - 对应任务表中的 `flow_task_id` 字段
2. **spaceId** - 对应任务表中的 `flow_serve_id` 字段

## 修改的文件

### 1. DataQualityPolicyDetailVO.java
添加了两个新字段：
```java
private String nodeRedId;
private String spaceId;
```

### 2. DataQualityNewPolicyDetailServiceImpl.java
- 添加了 `DataQualityNewTaskService` 依赖注入
- 在 `getDetailByLibraryId` 方法中添加了查询任务信息的逻辑：
```java
// 查询相关任务的 flow_task_id 和 flow_serve_id
DataQualityTask task = dataQualityNewTaskService.getOne(new QueryWrapper<DataQualityTask>().eq("library_id", libraryId).last("LIMIT 1"));
if (task != null) {
    detailVO.setNodeRedId(task.getFlowTaskId());
    detailVO.setSpaceId(task.getFlowServeId());
}
```

## 接口测试

### 请求
```
POST /dataService_quality/service/dataQuality-new/getPolicyDetail?libraryId=21d790813cff2d07b815f0cafd907efc
```

### 预期响应
现在接口应该返回包含 `nodeRedId` 和 `spaceId` 字段的响应：
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": "...",
    "libraryId": "21d790813cff2d07b815f0cafd907efc",
    "policeName": "...",
    "nodeRedId": "任务的flow_task_id值",
    "spaceId": "任务的flow_serve_id值",
    // ... 其他现有字段
  }
}
```

## 注意事项

1. 如果该 `libraryId` 没有对应的任务记录，`nodeRedId` 和 `spaceId` 将为 `null`
2. 如果有多个任务对应同一个 `libraryId`，将返回第一个任务的信息（使用了 `LIMIT 1`）
3. 修改保持了向后兼容性，不会影响现有的接口功能
